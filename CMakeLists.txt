cmake_minimum_required(VERSION 3.16)
project(myapp_vtk_ubuntu)

set(CMAKE_CXX_STANDARD 20)

# 设置VTK路径 - 根据不同操作系统进行配置
#set(VTK_DIR "C:/Users/<USER>/Desktop/lib/vtk-v9.0.0-install/lib/cmake/vtk-9.0")
set(VTK_DIR "D:/msys64/mingw64/3rdparty/VTK9.0/lib/cmake/vtk-9.0")
message(STATUS "Using VTK_DIR: ${VTK_DIR}")
if(WIN32)
    # Windows系统下的VTK路径配置
    # 用户可能需要根据实际安装路径修改此处
#    set(VTK_DIR "C:/Users/<USER>/Desktop/lib/vtk-v9.0.0-install/lib/cmake/vtk-9.0")
    set(VTK_DIR "D:/msys64/mingw64/3rdparty/VTK9.0/lib/cmake/vtk-9.0")
    
else()
    # Linux系统下的VTK路径配置
    set(VTK_DIR "/home/<USER>/vtk-9.0.0-install/lib/cmake/vtk-9.0")
endif()

# 提示用户可能需要修改VTK路径
message(STATUS "Using VTK_DIR: ${VTK_DIR}")
message(STATUS "If VTK is not found, please modify the VTK_DIR variable to point to your VTK installation.")

# 查找VTK包
find_package(VTK REQUIRED COMPONENTS
    CommonCore
    CommonDataModel
    CommonExecutionModel
    CommonColor
    FiltersSources
    FiltersCore
    FiltersGeometry
    FiltersModeling
    FiltersGeneral
    FiltersExtraction
    RenderingCore
    RenderingOpenGL2
    RenderingFreeType
    InteractionStyle
    InteractionWidgets
    RenderingAnnotation
    IOPLY
    IOGeometry
)

# 如果找到VTK，显示版本信息
if(VTK_FOUND)
    message(STATUS "Found VTK: ${VTK_VERSION}")
else()
    message(FATAL_ERROR "VTK not found. Please set VTK_DIR correctly.")
endif()

# 创建可执行文件
add_executable(myapp_vtk_ubuntu main.cpp)

# 链接VTK库
target_link_libraries(myapp_vtk_ubuntu ${VTK_LIBRARIES})

# 设置VTK模块自动初始化
vtk_module_autoinit(
    TARGETS myapp_vtk_ubuntu
    MODULES ${VTK_LIBRARIES}
)
