#include <vtkActor.h>
#include <vtkNamedColors.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>

#include <iostream>

int main()
{
    std::cout << "=== 基础VTK PLY查看器 ===" << std::endl;

    // 创建颜色对象
    vtkNew<vtkNamedColors> colors;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    
    try {
        reader->Update();
        std::cout << "成功加载PLY文件" << std::endl;
    } catch (...) {
        std::cerr << "加载PLY文件失败" << std::endl;
        return -1;
    }

    // 创建映射器
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    // 创建演员
    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(colors->GetColor3d("Silver").GetData());

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(colors->GetColor3d("DarkSlateGray").GetData());

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK PLY查看器");
    renderWindow->SetSize(800, 600);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 开始渲染和交互
    renderWindow->Render();
    std::cout << "程序已启动，可以使用鼠标旋转和缩放模型" << std::endl;
    renderWindowInteractor->Start();

    return 0;
}
