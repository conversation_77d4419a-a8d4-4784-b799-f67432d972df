@echo off
echo 正在配置VTK项目...

REM 清理旧的构建目录
if exist cmake-build-debug rmdir /s /q cmake-build-debug

REM 创建构建目录
mkdir cmake-build-debug
cd cmake-build-debug

REM 配置项目
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release

REM 编译项目
if %errorlevel% equ 0 (
    echo 配置成功，开始编译...
    cmake --build . --config Release
    if %errorlevel% equ 0 (
        echo 编译成功！
        echo 可执行文件位于: cmake-build-debug\myapp_vtk_ubuntu.exe
    ) else (
        echo 编译失败！
    )
) else (
    echo 配置失败！
)

cd ..
pause
