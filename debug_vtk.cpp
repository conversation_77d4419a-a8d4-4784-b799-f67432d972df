#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>

#include <iostream>

int main()
{
    std::cout << "Starting VTK PLY Viewer..." << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    
    std::cout << "Loading PLY file..." << std::endl;
    reader->Update();
    
    vtkPolyData* polyData = reader->GetOutput();
    
    if (!polyData || polyData->GetNumberOfPoints() == 0)
    {
        std::cerr << "Error: Failed to load PLY file or file is empty!" << std::endl;
        return -1;
    }
    
    std::cout << "PLY file loaded successfully!" << std::endl;
    std::cout << "Number of points: " << polyData->GetNumberOfPoints() << std::endl;
    std::cout << "Number of cells: " << polyData->GetNumberOfCells() << std::endl;

    // 获取模型边界
    double bounds[6];
    polyData->GetBounds(bounds);
    std::cout << "Model bounds:" << std::endl;
    std::cout << "  X: [" << bounds[0] << ", " << bounds[1] << "]" << std::endl;
    std::cout << "  Y: [" << bounds[2] << ", " << bounds[3] << "]" << std::endl;
    std::cout << "  Z: [" << bounds[4] << ", " << bounds[5] << "]" << std::endl;

    // 创建映射器
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    // 创建演员
    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.9, 0.9, 0.9); // 更亮的灰色
    actor->GetProperty()->SetOpacity(1.0);
    actor->GetProperty()->SetAmbient(0.3); // 增加环境光
    actor->GetProperty()->SetDiffuse(0.7); // 增加漫反射
    actor->GetProperty()->SetSpecular(0.1); // 少量镜面反射

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.1, 0.2, 0.4); // 深蓝色背景

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK PLY Debug Viewer");
    renderWindow->SetSize(800, 600);

    // 设置相机
    vtkCamera* camera = renderer->GetActiveCamera();
    
    // 计算模型中心
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };
    
    // 计算模型大小
    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));
    
    std::cout << "Model center: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
    std::cout << "Model size: " << size << std::endl;
    
    // 设置相机位置 - 确保能看到模型
    // 由于模型在Z轴负方向，我们需要调整相机位置
    camera->SetPosition(center[0], center[1], center[2] + size * 2.0); // 从上方看
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0); // Y轴向上

    // 重置相机以确保模型完全可见
    renderer->ResetCamera();
    camera->Zoom(0.9); // 稍微缩小一点以确保完全可见

    std::cout << "Camera position set to: ("
              << center[0] << ", " << center[1] << ", " << (center[2] + size * 2.0) << ")" << std::endl;

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    std::cout << "Starting render..." << std::endl;
    
    // 开始渲染和交互
    renderWindow->Render();
    
    std::cout << "Render window created. Use mouse to interact:" << std::endl;
    std::cout << "- Left mouse: rotate" << std::endl;
    std::cout << "- Right mouse: zoom" << std::endl;
    std::cout << "- Middle mouse: pan" << std::endl;
    std::cout << "- Press 'q' to quit" << std::endl;
    
    renderWindowInteractor->Start();

    return 0;
}
