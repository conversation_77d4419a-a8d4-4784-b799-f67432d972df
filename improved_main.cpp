#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkCellPicker.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>
#include <vtkSphereSource.h>
#include <vtkUnsignedCharArray.h>
#include <vtkCellData.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
#include <vtkSmartPointer.h>
#include <vtkMath.h>
#include <vtkCell.h>
#include <vtkTriangle.h>
#include <vtkPoints.h>
#include <vtkCellArray.h>
#include <vtkPolyLine.h>

#include <iostream>
#include <vector>
#include <array>
#include <cmath>
#include <algorithm>

// 改进的交互器样式类，修复区域着色精度问题
class ImprovedMouseInteractorStyle : public vtkInteractorStyleTrackballCamera
{
public:
    static ImprovedMouseInteractorStyle* New();
    vtkTypeMacro(ImprovedMouseInteractorStyle, vtkInteractorStyleTrackballCamera);

    virtual void OnLeftButtonDown() override
    {
        // 获取鼠标点击位置
        int* clickPos = this->GetInteractor()->GetEventPosition();
        
        // 使用拾取器获取点击的表面点
        this->Picker->SetTolerance(0.001); // 提高拾取精度
        this->Picker->Pick(clickPos[0], clickPos[1], 0, this->CurrentRenderer);
        
        double* worldPosition = this->Picker->GetPickPosition();
        vtkIdType cellId = this->Picker->GetCellId();
        
        if (cellId != -1)
        {
            // 获取更精确的表面点位置
            double precisePosition[3];
            this->GetPrecisePickPosition(cellId, worldPosition, precisePosition);
            
            std::cout << "选中点 " << this->SelectedPoints.size() + 1
                      << ": (" << precisePosition[0] << ", " << precisePosition[1]
                      << ", " << precisePosition[2] << ")" << std::endl;
            
            // 存储选中的点
            this->SelectedPoints.push_back({precisePosition[0], precisePosition[1], precisePosition[2]});
            this->SelectedCellIds.push_back(cellId);
            
            // 添加可视化标记点
            this->AddMarkerSphere(precisePosition);
            
            // 更新状态显示
            this->UpdateStatusText();
            
            // 如果选择了足够的点（至少3个），进行区域着色
            if (this->SelectedPoints.size() >= 3)
            {
                this->ColorRegionImproved();
            }
            
            // 刷新渲染
            this->GetInteractor()->GetRenderWindow()->Render();
        }
        
        // 调用父类方法以保持正常的相机交互
        vtkInteractorStyleTrackballCamera::OnLeftButtonDown();
    }

    virtual void OnKeyPress() override
    {
        std::string key = this->GetInteractor()->GetKeySym();
        
        if (key == "r" || key == "R")
        {
            this->ResetSelection();
            std::cout << "已重置选择" << std::endl;
        }
        else if (key == "c" || key == "C")
        {
            this->ClearColoring();
            std::cout << "已清除着色" << std::endl;
        }
        else if (key == "d" || key == "D")
        {
            // 调试模式：显示投影信息
            this->DebugProjection();
        }
        
        vtkInteractorStyleTrackballCamera::OnKeyPress();
    }

    void SetPicker(vtkCellPicker* picker) { this->Picker = picker; }
    void SetPolyData(vtkPolyData* polyData) 
    { 
        this->PolyData = polyData;
        this->InitializeColors();
    }
    void SetRenderer(vtkRenderer* renderer) { this->CurrentRenderer = renderer; }
    void SetTextActor(vtkTextActor* textActor) { this->StatusTextActor = textActor; }

protected:
    ImprovedMouseInteractorStyle()
    {
        this->Picker = nullptr;
        this->PolyData = nullptr;
        this->CurrentRenderer = nullptr;
        this->StatusTextActor = nullptr;
        this->ProjectionTolerance = 1.0; // 投影容差
    }

    void InitializeColors()
    {
        if (!this->PolyData) return;
        
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        this->Colors = vtkSmartPointer<vtkUnsignedCharArray>::New();
        this->Colors->SetNumberOfComponents(3);
        this->Colors->SetName("Colors");
        this->Colors->SetNumberOfTuples(numCells);
        
        // 设置默认颜色为浅灰色
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->PolyData->GetCellData()->SetScalars(this->Colors);
    }

    void AddMarkerSphere(double* position)
    {
        vtkNew<vtkSphereSource> sphereSource;
        sphereSource->SetCenter(position[0], position[1], position[2]);
        sphereSource->SetRadius(5.0);
        sphereSource->Update();
        
        vtkNew<vtkPolyDataMapper> sphereMapper;
        sphereMapper->SetInputConnection(sphereSource->GetOutputPort());
        
        vtkNew<vtkActor> sphereActor;
        sphereActor->SetMapper(sphereMapper);
        sphereActor->GetProperty()->SetColor(1.0, 1.0, 0.0); // 黄色标记
        
        this->MarkerActors.push_back(sphereActor);
        this->CurrentRenderer->AddActor(sphereActor);
    }

    // 获取更精确的拾取位置
    void GetPrecisePickPosition(vtkIdType cellId, double* pickPos, double* precisePos)
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);
        
        // 如果是三角形，计算最近的表面点
        if (cell->GetCellType() == VTK_TRIANGLE)
        {
            vtkPoints* points = cell->GetPoints();
            double p0[3], p1[3], p2[3];
            points->GetPoint(0, p0);
            points->GetPoint(1, p1);
            points->GetPoint(2, p2);
            
            // 计算点到三角形的最近点
            double closestPoint[3];
            double t, pcoords[3];
            int subId;
            double dist2;
            
            cell->EvaluatePosition(pickPos, closestPoint, subId, pcoords, dist2, nullptr);
            
            precisePos[0] = closestPoint[0];
            precisePos[1] = closestPoint[1];
            precisePos[2] = closestPoint[2];
        }
        else
        {
            // 对于其他类型的cell，使用原始拾取位置
            precisePos[0] = pickPos[0];
            precisePos[1] = pickPos[1];
            precisePos[2] = pickPos[2];
        }
    }

private:
    vtkCellPicker* Picker;
    vtkPolyData* PolyData;
    vtkRenderer* CurrentRenderer;
    vtkTextActor* StatusTextActor;
    vtkSmartPointer<vtkUnsignedCharArray> Colors;
    
    std::vector<std::array<double, 3>> SelectedPoints;
    std::vector<vtkIdType> SelectedCellIds;
    std::vector<vtkSmartPointer<vtkActor>> MarkerActors;
    
    double ProjectionTolerance; // 投影容差
    
    void UpdateStatusText()
    {
        if (!this->StatusTextActor) return;
        
        std::string statusText = "已选择点数: " + std::to_string(this->SelectedPoints.size()) + 
                                "\n按 'R' 重置选择，按 'C' 清除着色，按 'D' 调试";
        this->StatusTextActor->SetInput(statusText.c_str());
    }
    
    void ResetSelection()
    {
        this->SelectedPoints.clear();
        this->SelectedCellIds.clear();
        
        for (auto& actor : this->MarkerActors)
        {
            this->CurrentRenderer->RemoveActor(actor);
        }
        this->MarkerActors.clear();
        
        this->ClearColoring();
        this->UpdateStatusText();
        this->GetInteractor()->GetRenderWindow()->Render();
    }
    
    void ClearColoring()
    {
        if (!this->PolyData || !this->Colors) return;
        
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->Colors->Modified();
        this->PolyData->Modified();
        this->GetInteractor()->GetRenderWindow()->Render();
    }
    
    void DebugProjection()
    {
        if (this->SelectedPoints.size() < 3) return;
        
        std::cout << "\n=== 投影调试信息 ===" << std::endl;
        std::cout << "选中点数量: " << this->SelectedPoints.size() << std::endl;
        
        for (size_t i = 0; i < this->SelectedPoints.size(); i++)
        {
            std::cout << "点 " << i+1 << ": (" 
                      << this->SelectedPoints[i][0] << ", "
                      << this->SelectedPoints[i][1] << ", "
                      << this->SelectedPoints[i][2] << ")" << std::endl;
        }
        
        // 计算并显示法向量
        double normal[3];
        this->ComputeOptimalNormal(normal);
        std::cout << "计算的法向量: (" << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        
        // 显示投影后的2D点
        std::vector<std::array<double, 2>> projectedPoints;
        this->ProjectPointsTo2DImproved(projectedPoints, normal);
        
        std::cout << "投影后的2D点:" << std::endl;
        for (size_t i = 0; i < projectedPoints.size(); i++)
        {
            std::cout << "  点 " << i+1 << ": (" 
                      << projectedPoints[i][0] << ", " << projectedPoints[i][1] << ")" << std::endl;
        }
        std::cout << "===================" << std::endl;
    }

    // 改进的区域着色算法
    void ColorRegionImproved()
    {
        if (this->SelectedPoints.size() < 3 || !this->PolyData || !this->Colors) return;

        std::cout << "开始精确区域着色..." << std::endl;

        // 1. 计算最优投影法向量
        double normal[3];
        this->ComputeOptimalNormal(normal);

        // 2. 将3D点投影到2D平面
        std::vector<std::array<double, 2>> projectedPolygon;
        this->ProjectPointsTo2DImproved(projectedPolygon, normal);

        // 3. 验证投影多边形的有效性
        if (!this->ValidateProjectedPolygon(projectedPolygon))
        {
            std::cout << "警告: 投影多边形无效，跳过着色" << std::endl;
            return;
        }

        // 4. 遍历所有面，进行精确的区域判断
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        int coloredCells = 0;

        for (vtkIdType cellId = 0; cellId < numCells; cellId++)
        {
            if (this->IsCellInRegion(cellId, normal, projectedPolygon))
            {
                this->Colors->SetTuple3(cellId, 255, 0, 0); // 红色
                coloredCells++;
            }
        }

        std::cout << "精确着色完成，共着色 " << coloredCells << " 个面" << std::endl;

        this->Colors->Modified();
        this->PolyData->Modified();
    }

    // 计算最优投影法向量
    void ComputeOptimalNormal(double normal[3])
    {
        normal[0] = normal[1] = normal[2] = 0.0;

        if (this->SelectedPoints.size() < 3) return;

        // 使用Newell方法计算多边形法向量（更稳定）
        for (size_t i = 0; i < this->SelectedPoints.size(); i++)
        {
            size_t next = (i + 1) % this->SelectedPoints.size();

            const auto& p1 = this->SelectedPoints[i];
            const auto& p2 = this->SelectedPoints[next];

            normal[0] += (p1[1] - p2[1]) * (p1[2] + p2[2]);
            normal[1] += (p1[2] - p2[2]) * (p1[0] + p2[0]);
            normal[2] += (p1[0] - p2[0]) * (p1[1] + p2[1]);
        }

        // 归一化法向量
        double magnitude = sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
        if (magnitude > 1e-10)
        {
            normal[0] /= magnitude;
            normal[1] /= magnitude;
            normal[2] /= magnitude;
        }
        else
        {
            // 如果计算失败，使用主成分分析方法
            this->ComputeNormalPCA(normal);
        }
    }

    // 使用主成分分析计算法向量
    void ComputeNormalPCA(double normal[3])
    {
        if (this->SelectedPoints.size() < 3)
        {
            normal[0] = 0.0; normal[1] = 0.0; normal[2] = 1.0;
            return;
        }

        // 计算质心
        double centroid[3] = {0.0, 0.0, 0.0};
        for (const auto& point : this->SelectedPoints)
        {
            centroid[0] += point[0];
            centroid[1] += point[1];
            centroid[2] += point[2];
        }
        centroid[0] /= this->SelectedPoints.size();
        centroid[1] /= this->SelectedPoints.size();
        centroid[2] /= this->SelectedPoints.size();

        // 计算协方差矩阵
        double covariance[3][3] = {{0.0}};
        for (const auto& point : this->SelectedPoints)
        {
            double dx = point[0] - centroid[0];
            double dy = point[1] - centroid[1];
            double dz = point[2] - centroid[2];

            covariance[0][0] += dx * dx;
            covariance[0][1] += dx * dy;
            covariance[0][2] += dx * dz;
            covariance[1][1] += dy * dy;
            covariance[1][2] += dy * dz;
            covariance[2][2] += dz * dz;
        }

        // 对称化协方差矩阵
        covariance[1][0] = covariance[0][1];
        covariance[2][0] = covariance[0][2];
        covariance[2][1] = covariance[1][2];

        // 简化的特征向量计算（取最小特征值对应的特征向量作为法向量）
        // 这里使用一个简化的方法，实际应用中可以使用更精确的特征值分解
        double minEigenvalue = covariance[0][0] + covariance[1][1] + covariance[2][2];
        normal[0] = 0.0; normal[1] = 0.0; normal[2] = 1.0;

        // 如果点都在一个平面上，使用叉积方法
        if (this->SelectedPoints.size() >= 3)
        {
            double v1[3] = {
                this->SelectedPoints[1][0] - this->SelectedPoints[0][0],
                this->SelectedPoints[1][1] - this->SelectedPoints[0][1],
                this->SelectedPoints[1][2] - this->SelectedPoints[0][2]
            };

            double v2[3] = {
                this->SelectedPoints[2][0] - this->SelectedPoints[0][0],
                this->SelectedPoints[2][1] - this->SelectedPoints[0][1],
                this->SelectedPoints[2][2] - this->SelectedPoints[0][2]
            };

            vtkMath::Cross(v1, v2, normal);
            vtkMath::Normalize(normal);
        }
    }

    // 改进的2D投影方法
    void ProjectPointsTo2DImproved(std::vector<std::array<double, 2>>& projectedPoints, double normal[3])
    {
        projectedPoints.clear();

        // 计算质心作为投影原点
        double centroid[3] = {0.0, 0.0, 0.0};
        for (const auto& point : this->SelectedPoints)
        {
            centroid[0] += point[0];
            centroid[1] += point[1];
            centroid[2] += point[2];
        }
        centroid[0] /= this->SelectedPoints.size();
        centroid[1] /= this->SelectedPoints.size();
        centroid[2] /= this->SelectedPoints.size();

        // 构建正交坐标系
        double u[3], v[3];
        this->BuildOrthogonalBasis(normal, u, v);

        // 投影所有点到2D平面
        for (const auto& point : this->SelectedPoints)
        {
            // 相对于质心的向量
            double relativePos[3] = {
                point[0] - centroid[0],
                point[1] - centroid[1],
                point[2] - centroid[2]
            };

            // 投影到2D坐标系
            double proj2D[2];
            proj2D[0] = vtkMath::Dot(relativePos, u);
            proj2D[1] = vtkMath::Dot(relativePos, v);

            projectedPoints.push_back({proj2D[0], proj2D[1]});
        }
    }

    // 构建正交基
    void BuildOrthogonalBasis(double normal[3], double u[3], double v[3])
    {
        // 找到与法向量最不平行的坐标轴
        double absNormal[3] = {fabs(normal[0]), fabs(normal[1]), fabs(normal[2])};
        int minAxis = 0;
        if (absNormal[1] < absNormal[minAxis]) minAxis = 1;
        if (absNormal[2] < absNormal[minAxis]) minAxis = 2;

        // 创建第一个正交向量
        double temp[3] = {0.0, 0.0, 0.0};
        temp[minAxis] = 1.0;

        // 使用Gram-Schmidt正交化
        vtkMath::Cross(normal, temp, v);
        vtkMath::Normalize(v);
        vtkMath::Cross(v, normal, u);
        vtkMath::Normalize(u);
    }

    // 验证投影多边形的有效性
    bool ValidateProjectedPolygon(const std::vector<std::array<double, 2>>& polygon)
    {
        if (polygon.size() < 3) return false;

        // 检查多边形面积
        double area = this->ComputePolygonArea(polygon);
        if (area < 1e-10) return false; // 面积太小

        // 检查是否有重复点
        for (size_t i = 0; i < polygon.size(); i++)
        {
            for (size_t j = i + 1; j < polygon.size(); j++)
            {
                double dx = polygon[i][0] - polygon[j][0];
                double dy = polygon[i][1] - polygon[j][1];
                if (sqrt(dx*dx + dy*dy) < 1e-6) return false; // 点太接近
            }
        }

        return true;
    }

    // 计算2D多边形面积
    double ComputePolygonArea(const std::vector<std::array<double, 2>>& polygon)
    {
        double area = 0.0;
        size_t n = polygon.size();

        for (size_t i = 0; i < n; i++)
        {
            size_t j = (i + 1) % n;
            area += polygon[i][0] * polygon[j][1];
            area -= polygon[j][0] * polygon[i][1];
        }

        return fabs(area) / 2.0;
    }

    // 判断面是否在选定区域内（改进版本）
    bool IsCellInRegion(vtkIdType cellId, double normal[3], const std::vector<std::array<double, 2>>& projectedPolygon)
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);

        // 获取面的所有顶点
        vtkPoints* cellPoints = cell->GetPoints();
        int numPoints = cellPoints->GetNumberOfPoints();

        // 检查面的多个采样点，而不仅仅是中心点
        std::vector<std::array<double, 3>> samplePoints;

        if (cell->GetCellType() == VTK_TRIANGLE && numPoints == 3)
        {
            // 对于三角形，采样多个点
            double p0[3], p1[3], p2[3];
            cellPoints->GetPoint(0, p0);
            cellPoints->GetPoint(1, p1);
            cellPoints->GetPoint(2, p2);

            // 采样点：顶点 + 边中点 + 重心
            samplePoints.resize(7);

            // 三个顶点
            samplePoints[0] = {p0[0], p0[1], p0[2]};
            samplePoints[1] = {p1[0], p1[1], p1[2]};
            samplePoints[2] = {p2[0], p2[1], p2[2]};

            // 三个边中点
            samplePoints[3] = {(p0[0] + p1[0]) / 2.0, (p0[1] + p1[1]) / 2.0, (p0[2] + p1[2]) / 2.0};
            samplePoints[4] = {(p1[0] + p2[0]) / 2.0, (p1[1] + p2[1]) / 2.0, (p1[2] + p2[2]) / 2.0};
            samplePoints[5] = {(p2[0] + p0[0]) / 2.0, (p2[1] + p0[1]) / 2.0, (p2[2] + p0[2]) / 2.0};

            // 重心
            samplePoints[6] = {(p0[0] + p1[0] + p2[0]) / 3.0, (p0[1] + p1[1] + p2[1]) / 3.0, (p0[2] + p1[2] + p2[2]) / 3.0};
        }
        else
        {
            // 对于其他类型的面，使用中心点
            samplePoints.resize(1);
            double center[3];
            this->GetCellCenterImproved(cellId, center);
            samplePoints[0] = {center[0], center[1], center[2]};
        }

        // 检查采样点，如果大部分点在多边形内，则认为面在区域内
        int pointsInside = 0;
        for (const auto& samplePoint : samplePoints)
        {
            double point[3] = {samplePoint[0], samplePoint[1], samplePoint[2]};
            if (this->IsPoint3DInPolygonRegion(point, normal, projectedPolygon))
            {
                pointsInside++;
            }
        }

        // 如果超过一半的采样点在多边形内，则认为面在区域内
        return pointsInside > samplePoints.size() / 2;
    }

    // 改进的面中心计算
    void GetCellCenterImproved(vtkIdType cellId, double center[3])
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);
        vtkPoints* points = cell->GetPoints();
        int numPoints = points->GetNumberOfPoints();

        center[0] = center[1] = center[2] = 0.0;

        // 计算所有顶点的平均位置
        for (int i = 0; i < numPoints; i++)
        {
            double point[3];
            points->GetPoint(i, point);
            center[0] += point[0];
            center[1] += point[1];
            center[2] += point[2];
        }

        center[0] /= numPoints;
        center[1] /= numPoints;
        center[2] /= numPoints;
    }

    // 判断3D点是否在多边形区域内
    bool IsPoint3DInPolygonRegion(double point3D[3], double normal[3], const std::vector<std::array<double, 2>>& projectedPolygon)
    {
        // 计算质心
        double centroid[3] = {0.0, 0.0, 0.0};
        for (const auto& selectedPoint : this->SelectedPoints)
        {
            centroid[0] += selectedPoint[0];
            centroid[1] += selectedPoint[1];
            centroid[2] += selectedPoint[2];
        }
        centroid[0] /= this->SelectedPoints.size();
        centroid[1] /= this->SelectedPoints.size();
        centroid[2] /= this->SelectedPoints.size();

        // 构建正交基
        double u[3], v[3];
        this->BuildOrthogonalBasis(normal, u, v);

        // 将3D点投影到2D
        double relativePos[3] = {
            point3D[0] - centroid[0],
            point3D[1] - centroid[1],
            point3D[2] - centroid[2]
        };

        double projectedPoint[2];
        projectedPoint[0] = vtkMath::Dot(relativePos, u);
        projectedPoint[1] = vtkMath::Dot(relativePos, v);

        // 使用改进的点在多边形内判断算法
        return this->IsPointInPolygonImproved(projectedPoint, projectedPolygon);
    }

    // 改进的点在多边形内判断算法（Winding Number方法）
    bool IsPointInPolygonImproved(double point[2], const std::vector<std::array<double, 2>>& polygon)
    {
        int windingNumber = 0;
        size_t n = polygon.size();

        for (size_t i = 0; i < n; i++)
        {
            size_t j = (i + 1) % n;

            if (polygon[i][1] <= point[1])
            {
                if (polygon[j][1] > point[1]) // 向上穿越
                {
                    if (this->IsLeft(polygon[i], polygon[j], point) > 0)
                    {
                        windingNumber++;
                    }
                }
            }
            else
            {
                if (polygon[j][1] <= point[1]) // 向下穿越
                {
                    if (this->IsLeft(polygon[i], polygon[j], point) < 0)
                    {
                        windingNumber--;
                    }
                }
            }
        }

        return windingNumber != 0;
    }

    // 判断点在线段的左侧还是右侧
    double IsLeft(const std::array<double, 2>& p0, const std::array<double, 2>& p1, double point[2])
    {
        return ((p1[0] - p0[0]) * (point[1] - p0[1]) - (point[0] - p0[0]) * (p1[1] - p0[1]));
    }
};

vtkStandardNewMacro(ImprovedMouseInteractorStyle);

int main()
{
    std::cout << "=== VTK 精确交互式区域着色应用程序 ===" << std::endl;
    std::cout << "改进功能：" << std::endl;
    std::cout << "1. 精确的3D到2D投影算法" << std::endl;
    std::cout << "2. 改进的点在多边形内判断（Winding Number）" << std::endl;
    std::cout << "3. 多点采样的面区域判断" << std::endl;
    std::cout << "4. 优化的法向量计算（Newell + PCA）" << std::endl;
    std::cout << "5. 投影有效性验证" << std::endl;
    std::cout << "操作说明：" << std::endl;
    std::cout << "- 鼠标左键：选择表面点" << std::endl;
    std::cout << "- R键：重置选择" << std::endl;
    std::cout << "- C键：清除着色" << std::endl;
    std::cout << "- D键：显示调试信息" << std::endl;
    std::cout << "========================================" << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "成功加载PLY文件" << std::endl;

    vtkPolyData* polyData = reader->GetOutput();
    std::cout << "模型信息 - 顶点数: " << polyData->GetNumberOfPoints()
              << ", 面数: " << polyData->GetNumberOfCells() << std::endl;

    // 创建映射器
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    // 创建演员
    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.9, 0.9, 0.9); // 更亮的灰色
    actor->GetProperty()->SetAmbient(0.3);
    actor->GetProperty()->SetDiffuse(0.7);
    actor->GetProperty()->SetSpecular(0.1);

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.1, 0.2, 0.4); // 深蓝色背景

    // 创建状态文本显示
    vtkNew<vtkTextActor> statusTextActor;
    statusTextActor->SetPosition(10, 10);
    statusTextActor->GetTextProperty()->SetFontSize(16);
    statusTextActor->GetTextProperty()->SetColor(1.0, 1.0, 1.0);
    statusTextActor->SetInput("已选择点数: 0\n按 'R' 重置选择，按 'C' 清除着色，按 'D' 调试");
    renderer->AddActor2D(statusTextActor);

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK 精确交互式区域着色");
    renderWindow->SetSize(1200, 800);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 创建拾取器
    vtkNew<vtkCellPicker> picker;

    // 创建改进的交互器样式
    vtkNew<ImprovedMouseInteractorStyle> style;
    style->SetPicker(picker);
    style->SetPolyData(polyData);
    style->SetRenderer(renderer);
    style->SetTextActor(statusTextActor);

    renderWindowInteractor->SetInteractorStyle(style);

    // 设置相机位置
    vtkCamera* camera = renderer->GetActiveCamera();

    double bounds[6];
    polyData->GetBounds(bounds);

    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };

    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));

    // 设置相机位置 - 确保能看到模型
    camera->SetPosition(center[0], center[1], center[2] + size * 2.0);
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0);

    renderer->ResetCamera();
    camera->Zoom(0.9);

    std::cout << "模型边界: X[" << bounds[0] << ", " << bounds[1] << "] "
              << "Y[" << bounds[2] << ", " << bounds[3] << "] "
              << "Z[" << bounds[4] << ", " << bounds[5] << "]" << std::endl;
    std::cout << "模型中心: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;

    // 开始渲染和交互
    renderWindow->Render();

    std::cout << "\n程序已启动，开始精确的交互式区域着色..." << std::endl;

    renderWindowInteractor->Start();

    return EXIT_SUCCESS;
}
