#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkCellPicker.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>
#include <vtkSphereSource.h>
#include <vtkUnsignedCharArray.h>
#include <vtkCellData.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
#include <vtkSmartPointer.h>
#include <vtkMath.h>
#include <vtkCell.h>
#include <vtkPolyDataNormals.h>
#include <vtkFloatArray.h>
#include <vtkPointData.h>

#include <iostream>
#include <vector>
#include <array>
#include <cmath>

// 自定义交互器样式类，用于处理鼠标点击事件
class MouseInteractorStyle : public vtkInteractorStyleTrackballCamera
{
public:
    static MouseInteractorStyle* New();
    vtkTypeMacro(MouseInteractorStyle, vtkInteractorStyleTrackballCamera);

    virtual void OnLeftButtonDown() override
    {
        // 获取鼠标点击位置
        int* clickPos = this->GetInteractor()->GetEventPosition();

        // 使用拾取器获取点击的表面点
        this->Picker->SetTolerance(0.0005);
        this->Picker->Pick(clickPos[0], clickPos[1], 0, this->CurrentRenderer);

        double* worldPosition = this->Picker->GetPickPosition();
        vtkIdType cellId = this->Picker->GetCellId();

        if (cellId != -1)
        {
            // 获取更精确的表面点位置
            double precisePosition[3];
            this->GetPrecisePickPosition(cellId, worldPosition, precisePosition);

            // 显示选中点信息
            std::cout << "选中点 " << this->SelectedPoints.size() + 1
                      << ": (" << precisePosition[0] << ", " << precisePosition[1]
                      << ", " << precisePosition[2] << ")" << std::endl;

            // 显示对应面的法向量信息
            this->DisplayCellNormalInfo(cellId);

            // 存储选中的点
            this->SelectedPoints.push_back({precisePosition[0], precisePosition[1], precisePosition[2]});
            this->SelectedCellIds.push_back(cellId);

            // 添加可视化标记点
            this->AddMarkerSphere(precisePosition);

            // 更新状态显示
            this->UpdateStatusText();

            // 如果选择了足够的点（至少3个），进行区域着色
            if (this->SelectedPoints.size() >= 3)
            {
                this->ColorRegion();
            }

            // 刷新渲染
            this->GetInteractor()->GetRenderWindow()->Render();
        }

        // 调用父类方法以保持正常的相机交互
        vtkInteractorStyleTrackballCamera::OnLeftButtonDown();
    }

    virtual void OnKeyPress() override
    {
        // 获取按键
        std::string key = this->GetInteractor()->GetKeySym();

        if (key == "r" || key == "R")
        {
            // 重置选择
            this->ResetSelection();
            std::cout << "已重置选择" << std::endl;
        }
        else if (key == "c" || key == "C")
        {
            // 清除着色
            this->ClearColoring();
            std::cout << "已清除着色" << std::endl;
        }
        else if (key == "d" || key == "D")
        {
            // 调试模式：显示投影信息
            this->DebugProjection();
        }

        // 调用父类方法
        vtkInteractorStyleTrackballCamera::OnKeyPress();
    }

    void SetPicker(vtkCellPicker* picker)
    {
        this->Picker = picker;
    }

    void SetPolyData(vtkPolyData* polyData)
    {
        this->PolyData = polyData;
        this->InitializeColors();
    }

    void SetRenderer(vtkRenderer* renderer)
    {
        this->CurrentRenderer = renderer;
    }

    void SetTextActor(vtkTextActor* textActor)
    {
        this->StatusTextActor = textActor;
    }

protected:
    MouseInteractorStyle()
    {
        this->Picker = nullptr;
        this->PolyData = nullptr;
        this->CurrentRenderer = nullptr;
        this->StatusTextActor = nullptr;
    }

    void InitializeColors()
    {
        if (!this->PolyData) return;

        // 初始化颜色数组，所有面都设为白色
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        this->Colors = vtkSmartPointer<vtkUnsignedCharArray>::New();
        this->Colors->SetNumberOfComponents(3);
        this->Colors->SetName("Colors");
        this->Colors->SetNumberOfTuples(numCells);

        // 设置默认颜色为浅灰色
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200); // 浅灰色
        }

        this->PolyData->GetCellData()->SetScalars(this->Colors);
    }

    void AddMarkerSphere(double* position)
    {
        // 创建小球标记选中的点
        vtkNew<vtkSphereSource> sphereSource;
        sphereSource->SetCenter(position[0], position[1], position[2]);
        sphereSource->SetRadius(5.0); // 根据模型大小调整（模型尺寸约542）
        sphereSource->Update();

        vtkNew<vtkPolyDataMapper> sphereMapper;
        sphereMapper->SetInputConnection(sphereSource->GetOutputPort());

        vtkNew<vtkActor> sphereActor;
        sphereActor->SetMapper(sphereMapper);
        sphereActor->GetProperty()->SetColor(1.0, 1.0, 0.0); // 黄色标记

        this->MarkerActors.push_back(sphereActor);
        this->CurrentRenderer->AddActor(sphereActor);
    }

private:
    vtkCellPicker* Picker;
    vtkPolyData* PolyData;
    vtkRenderer* CurrentRenderer;
    vtkTextActor* StatusTextActor;
    vtkSmartPointer<vtkUnsignedCharArray> Colors;

    std::vector<std::array<double, 3>> SelectedPoints;
    std::vector<vtkIdType> SelectedCellIds;
    std::vector<vtkSmartPointer<vtkActor>> MarkerActors;

    void UpdateStatusText()
    {
        if (!this->StatusTextActor) return;

        std::string statusText = "已选择点数: " + std::to_string(this->SelectedPoints.size()) +
                                "\n按 'R' 重置选择，按 'C' 清除着色，按 'D' 调试";
        this->StatusTextActor->SetInput(statusText.c_str());
    }

    void ResetSelection()
    {
        // 清除选中的点
        this->SelectedPoints.clear();
        this->SelectedCellIds.clear();

        // 移除标记球
        for (auto& actor : this->MarkerActors)
        {
            this->CurrentRenderer->RemoveActor(actor);
        }
        this->MarkerActors.clear();

        // 重置颜色
        this->ClearColoring();

        // 更新状态
        this->UpdateStatusText();

        // 刷新渲染
        this->GetInteractor()->GetRenderWindow()->Render();
    }

    void ClearColoring()
    {
        if (!this->PolyData || !this->Colors) return;

        // 重置所有面的颜色为浅灰色
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }

        this->Colors->Modified();
        this->PolyData->Modified();

        // 刷新渲染
        this->GetInteractor()->GetRenderWindow()->Render();
    }

    void ColorRegion()
    {
        if (this->SelectedPoints.size() < 3 || !this->PolyData || !this->Colors) return;

        std::cout << "开始精确区域着色..." << std::endl;
        std::cout << "选中点数量: " << this->SelectedPoints.size() << std::endl;

        // 计算选中点的平均法向量，用于投影平面
        double avgNormal[3] = {0.0, 0.0, 0.0};
        this->ComputeAverageNormal(avgNormal);

        std::cout << "计算的法向量: (" << avgNormal[0] << ", " << avgNormal[1] << ", " << avgNormal[2] << ")" << std::endl;

        // 将3D点投影到2D平面
        std::vector<std::array<double, 2>> projectedPoints;
        this->ProjectPointsTo2D(projectedPoints, avgNormal);

        // 验证投影多边形
        double polygonArea = this->ComputePolygonArea(projectedPoints);
        std::cout << "投影多边形面积: " << polygonArea << std::endl;

        if (polygonArea < 1e-6)
        {
            std::cout << "警告: 投影多边形面积太小，可能投影失败" << std::endl;
            return;
        }

        // 遍历所有面，检查是否在选中区域内
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        int coloredCells = 0;

        for (vtkIdType cellId = 0; cellId < numCells; cellId++)
        {
            // 获取面的中心点
            double cellCenter[3];
            this->GetCellCenter(cellId, cellCenter);

            // 将面中心投影到2D平面
            double projectedCenter[2];
            this->ProjectPointTo2D(cellCenter, avgNormal, projectedCenter);

            // 检查投影点是否在多边形内
            if (this->IsPointInPolygon(projectedCenter, projectedPoints))
            {
                // 设置为红色
                this->Colors->SetTuple3(cellId, 255, 0, 0);
                coloredCells++;
            }
        }

        std::cout << "精确着色完成，共着色 " << coloredCells << " 个面" << std::endl;

        this->Colors->Modified();
        this->PolyData->Modified();
    }

    // 计算2D多边形面积
    double ComputePolygonArea(const std::vector<std::array<double, 2>>& polygon)
    {
        double area = 0.0;
        size_t n = polygon.size();

        for (size_t i = 0; i < n; i++)
        {
            size_t j = (i + 1) % n;
            area += polygon[i][0] * polygon[j][1];
            area -= polygon[j][0] * polygon[i][1];
        }

        return fabs(area) / 2.0;
    }

    void ComputeAverageNormal(double avgNormal[3])
    {
        avgNormal[0] = avgNormal[1] = avgNormal[2] = 0.0;

        if (this->SelectedPoints.size() < 3)
        {
            avgNormal[0] = 0.0; avgNormal[1] = 0.0; avgNormal[2] = 1.0;
            return;
        }

        // 方法1: 使用选中点对应的表面法向量的平均值
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        if (cellNormals && this->SelectedCellIds.size() >= 3)
        {
            std::cout << "使用预计算的表面法向量..." << std::endl;

            for (vtkIdType cellId : this->SelectedCellIds)
            {
                double cellNormal[3];
                cellNormals->GetTuple(cellId, cellNormal);

                avgNormal[0] += cellNormal[0];
                avgNormal[1] += cellNormal[1];
                avgNormal[2] += cellNormal[2];
            }

            // 归一化
            double magnitude = sqrt(avgNormal[0]*avgNormal[0] + avgNormal[1]*avgNormal[1] + avgNormal[2]*avgNormal[2]);
            if (magnitude > 1e-10)
            {
                avgNormal[0] /= magnitude;
                avgNormal[1] /= magnitude;
                avgNormal[2] /= magnitude;

                std::cout << "使用表面法向量计算成功" << std::endl;
                return;
            }
        }

        std::cout << "使用Newell方法计算法向量..." << std::endl;

        // 方法2: 使用Newell方法计算多边形法向量（更稳定）
        for (size_t i = 0; i < this->SelectedPoints.size(); i++)
        {
            size_t next = (i + 1) % this->SelectedPoints.size();

            const auto& p1 = this->SelectedPoints[i];
            const auto& p2 = this->SelectedPoints[next];

            avgNormal[0] += (p1[1] - p2[1]) * (p1[2] + p2[2]);
            avgNormal[1] += (p1[2] - p2[2]) * (p1[0] + p2[0]);
            avgNormal[2] += (p1[0] - p2[0]) * (p1[1] + p2[1]);
        }

        // 归一化法向量
        double magnitude = sqrt(avgNormal[0]*avgNormal[0] + avgNormal[1]*avgNormal[1] + avgNormal[2]*avgNormal[2]);
        if (magnitude > 1e-10)
        {
            avgNormal[0] /= magnitude;
            avgNormal[1] /= magnitude;
            avgNormal[2] /= magnitude;
        }
        else
        {
            // 方法3: 如果Newell方法失败，使用叉积方法
            std::cout << "使用叉积方法计算法向量..." << std::endl;

            double v1[3] = {
                this->SelectedPoints[1][0] - this->SelectedPoints[0][0],
                this->SelectedPoints[1][1] - this->SelectedPoints[0][1],
                this->SelectedPoints[1][2] - this->SelectedPoints[0][2]
            };

            double v2[3] = {
                this->SelectedPoints[2][0] - this->SelectedPoints[0][0],
                this->SelectedPoints[2][1] - this->SelectedPoints[0][1],
                this->SelectedPoints[2][2] - this->SelectedPoints[0][2]
            };

            vtkMath::Cross(v1, v2, avgNormal);
            vtkMath::Normalize(avgNormal);

            // 如果还是失败，使用默认方向
            if (vtkMath::Norm(avgNormal) < 0.1)
            {
                avgNormal[0] = 0.0;
                avgNormal[1] = 0.0;
                avgNormal[2] = 1.0;
                std::cout << "使用默认Z轴方向" << std::endl;
            }
        }
    }

    void ProjectPointsTo2D(std::vector<std::array<double, 2>>& projectedPoints, double normal[3])
    {
        projectedPoints.clear();

        // 计算质心作为投影原点
        double centroid[3] = {0.0, 0.0, 0.0};
        for (const auto& point : this->SelectedPoints)
        {
            centroid[0] += point[0];
            centroid[1] += point[1];
            centroid[2] += point[2];
        }
        centroid[0] /= this->SelectedPoints.size();
        centroid[1] /= this->SelectedPoints.size();
        centroid[2] /= this->SelectedPoints.size();

        // 创建投影平面的两个正交向量
        double u[3], v[3];

        // 找到与法向量最不平行的坐标轴
        double absNormal[3] = {fabs(normal[0]), fabs(normal[1]), fabs(normal[2])};
        int minAxis = 0;
        if (absNormal[1] < absNormal[minAxis]) minAxis = 1;
        if (absNormal[2] < absNormal[minAxis]) minAxis = 2;

        // 创建第一个正交向量
        double temp[3] = {0.0, 0.0, 0.0};
        temp[minAxis] = 1.0;

        // 使用Gram-Schmidt正交化
        vtkMath::Cross(normal, temp, v);
        vtkMath::Normalize(v);
        vtkMath::Cross(v, normal, u);
        vtkMath::Normalize(u);

        // 投影所有选中的点到2D平面
        for (const auto& point : this->SelectedPoints)
        {
            // 相对于质心的向量
            double relativePos[3] = {
                point[0] - centroid[0],
                point[1] - centroid[1],
                point[2] - centroid[2]
            };

            // 投影到2D坐标系
            double proj2D[2];
            proj2D[0] = vtkMath::Dot(relativePos, u);
            proj2D[1] = vtkMath::Dot(relativePos, v);
            projectedPoints.push_back({proj2D[0], proj2D[1]});
        }
    }

    void ProjectPointTo2D(double point3D[3], double normal[3], double point2D[2])
    {
        // 计算质心
        double centroid[3] = {0.0, 0.0, 0.0};
        for (const auto& point : this->SelectedPoints)
        {
            centroid[0] += point[0];
            centroid[1] += point[1];
            centroid[2] += point[2];
        }
        centroid[0] /= this->SelectedPoints.size();
        centroid[1] /= this->SelectedPoints.size();
        centroid[2] /= this->SelectedPoints.size();

        // 使用与ProjectPointsTo2D相同的投影方法
        double u[3], v[3];

        double absNormal[3] = {fabs(normal[0]), fabs(normal[1]), fabs(normal[2])};
        int minAxis = 0;
        if (absNormal[1] < absNormal[minAxis]) minAxis = 1;
        if (absNormal[2] < absNormal[minAxis]) minAxis = 2;

        double temp[3] = {0.0, 0.0, 0.0};
        temp[minAxis] = 1.0;

        vtkMath::Cross(normal, temp, v);
        vtkMath::Normalize(v);
        vtkMath::Cross(v, normal, u);
        vtkMath::Normalize(u);

        // 相对于质心的向量
        double relativePos[3] = {
            point3D[0] - centroid[0],
            point3D[1] - centroid[1],
            point3D[2] - centroid[2]
        };

        point2D[0] = vtkMath::Dot(relativePos, u);
        point2D[1] = vtkMath::Dot(relativePos, v);
    }

    void GetCellCenter(vtkIdType cellId, double center[3])
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);
        double pcoords[3] = {0.5, 0.5, 0.5};
        double weights[VTK_CELL_SIZE];

        int subId = 0;
        cell->EvaluateLocation(subId, pcoords, center, weights);
    }

    bool IsPointInPolygon(double point[2], const std::vector<std::array<double, 2>>& polygon)
    {
        // 改进的点在多边形内判断算法（Winding Number方法）
        int windingNumber = 0;
        size_t n = polygon.size();

        for (size_t i = 0; i < n; i++)
        {
            size_t j = (i + 1) % n;

            if (polygon[i][1] <= point[1])
            {
                if (polygon[j][1] > point[1]) // 向上穿越
                {
                    if (this->IsLeft(polygon[i], polygon[j], point) > 0)
                    {
                        windingNumber++;
                    }
                }
            }
            else
            {
                if (polygon[j][1] <= point[1]) // 向下穿越
                {
                    if (this->IsLeft(polygon[i], polygon[j], point) < 0)
                    {
                        windingNumber--;
                    }
                }
            }
        }

        return windingNumber != 0;
    }

    // 判断点在线段的左侧还是右侧
    double IsLeft(const std::array<double, 2>& p0, const std::array<double, 2>& p1, double point[2])
    {
        return ((p1[0] - p0[0]) * (point[1] - p0[1]) - (point[0] - p0[0]) * (p1[1] - p0[1]));
    }

    void DebugProjection()
    {
        if (this->SelectedPoints.size() < 3)
        {
            std::cout << "需要至少3个点才能进行调试" << std::endl;
            return;
        }

        std::cout << "\n=== 投影调试信息 ===" << std::endl;
        std::cout << "选中点数量: " << this->SelectedPoints.size() << std::endl;

        for (size_t i = 0; i < this->SelectedPoints.size(); i++)
        {
            std::cout << "点 " << i+1 << ": ("
                      << this->SelectedPoints[i][0] << ", "
                      << this->SelectedPoints[i][1] << ", "
                      << this->SelectedPoints[i][2] << ")" << std::endl;
        }

        // 显示每个选中面的法向量
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        if (cellNormals)
        {
            std::cout << "各面的法向量:" << std::endl;
            for (size_t i = 0; i < this->SelectedCellIds.size(); i++)
            {
                double normal[3];
                cellNormals->GetTuple(this->SelectedCellIds[i], normal);
                std::cout << "  面 " << i+1 << " (ID:" << this->SelectedCellIds[i] << "): ("
                          << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
            }
        }

        // 计算并显示平均法向量
        double avgNormal[3];
        this->ComputeAverageNormal(avgNormal);
        std::cout << "计算的平均法向量: (" << avgNormal[0] << ", " << avgNormal[1] << ", " << avgNormal[2] << ")" << std::endl;

        // 显示投影后的2D点
        std::vector<std::array<double, 2>> projectedPoints;
        this->ProjectPointsTo2D(projectedPoints, avgNormal);

        std::cout << "投影后的2D点:" << std::endl;
        for (size_t i = 0; i < projectedPoints.size(); i++)
        {
            std::cout << "  点 " << i+1 << ": ("
                      << projectedPoints[i][0] << ", " << projectedPoints[i][1] << ")" << std::endl;
        }

        double area = this->ComputePolygonArea(projectedPoints);
        std::cout << "投影多边形面积: " << area << std::endl;
        std::cout << "===================" << std::endl;
    }

    // 获取更精确的拾取位置
    void GetPrecisePickPosition(vtkIdType cellId, double* pickPos, double* precisePos)
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);

        // 如果是三角形，计算最近的表面点
        if (cell->GetCellType() == VTK_TRIANGLE)
        {
            vtkPoints* points = cell->GetPoints();
            double p0[3], p1[3], p2[3];
            points->GetPoint(0, p0);
            points->GetPoint(1, p1);
            points->GetPoint(2, p2);

            // 计算点到三角形的最近点
            double closestPoint[3];
            double t, pcoords[3];
            int subId;
            double dist2;

            cell->EvaluatePosition(pickPos, closestPoint, subId, pcoords, dist2, nullptr);

            precisePos[0] = closestPoint[0];
            precisePos[1] = closestPoint[1];
            precisePos[2] = closestPoint[2];
        }
        else
        {
            // 对于其他类型的cell，使用原始拾取位置
            precisePos[0] = pickPos[0];
            precisePos[1] = pickPos[1];
            precisePos[2] = pickPos[2];
        }
    }

    // 显示面的法向量信息
    void DisplayCellNormalInfo(vtkIdType cellId)
    {
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        if (cellNormals)
        {
            double normal[3];
            cellNormals->GetTuple(cellId, normal);
            std::cout << "  面法向量: (" << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        }
        else
        {
            std::cout << "  警告: 该面没有法向量信息" << std::endl;
        }
    }
};

vtkStandardNewMacro(MouseInteractorStyle);

int main()
{
    std::cout << "=== VTK 交互式区域着色应用程序 ===" << std::endl;
    std::cout << "功能说明：" << std::endl;
    std::cout << "1. 鼠标左键点击选择表面点" << std::endl;
    std::cout << "2. 选择3个或更多点后自动形成封闭区域并着色" << std::endl;
    std::cout << "3. 按 'R' 键重置选择" << std::endl;
    std::cout << "4. 按 'C' 键清除着色" << std::endl;
    std::cout << "5. 鼠标右键拖拽旋转视角，滚轮缩放" << std::endl;
    std::cout << "=================================" << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "成功加载PLY文件: mesh20240409.ply" << std::endl;

    vtkPolyData* polyData = reader->GetOutput();
    std::cout << "模型信息 - 顶点数: " << polyData->GetNumberOfPoints()
              << ", 面数: " << polyData->GetNumberOfCells() << std::endl;

    // 计算模型的法向量
    std::cout << "正在计算模型法向量..." << std::endl;
    vtkNew<vtkPolyDataNormals> normalGenerator;
    normalGenerator->SetInputData(polyData);
    normalGenerator->ComputePointNormalsOn();  // 计算点法向量
    normalGenerator->ComputeCellNormalsOn();   // 计算面法向量
    normalGenerator->SplittingOff();           // 不分割共享顶点
    normalGenerator->ConsistencyOn();          // 确保法向量方向一致
    normalGenerator->AutoOrientNormalsOn();   // 自动调整法向量方向
    normalGenerator->Update();

    // 使用计算了法向量的数据
    polyData = normalGenerator->GetOutput();
    std::cout << "法向量计算完成" << std::endl;

    // 验证法向量是否计算成功
    vtkDataArray* pointNormals = polyData->GetPointData()->GetNormals();
    vtkDataArray* cellNormals = polyData->GetCellData()->GetNormals();

    if (pointNormals)
    {
        std::cout << "点法向量数量: " << pointNormals->GetNumberOfTuples() << std::endl;
    }
    else
    {
        std::cout << "警告: 点法向量计算失败" << std::endl;
    }

    if (cellNormals)
    {
        std::cout << "面法向量数量: " << cellNormals->GetNumberOfTuples() << std::endl;
    }
    else
    {
        std::cout << "警告: 面法向量计算失败" << std::endl;
    }

    // 创建映射器
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    // 创建演员
    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.8, 0.8, 0.8); // 浅灰色

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.2, 0.3, 0.4); // 深蓝灰色

    // 创建状态文本显示
    vtkNew<vtkTextActor> statusTextActor;
    statusTextActor->SetPosition(10, 10);
    statusTextActor->GetTextProperty()->SetFontSize(16);
    statusTextActor->GetTextProperty()->SetColor(1.0, 1.0, 1.0); // 白色
    statusTextActor->SetInput("已选择点数: 0\n按 'R' 重置选择，按 'C' 清除着色，按 'D' 调试");
    renderer->AddActor2D(statusTextActor);

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK 交互式区域着色");
    renderWindow->SetSize(1200, 800);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 创建拾取器
    vtkNew<vtkCellPicker> picker;

    // 创建自定义交互器样式
    vtkNew<MouseInteractorStyle> style;
    style->SetPicker(picker);
    style->SetPolyData(polyData);
    style->SetRenderer(renderer);
    style->SetTextActor(statusTextActor);

    renderWindowInteractor->SetInteractorStyle(style);

    // 设置相机位置以获得更好的初始视角
    vtkCamera* camera = renderer->GetActiveCamera();

    // 获取模型的边界框
    double bounds[6];
    polyData->GetBounds(bounds);

    // 计算模型中心
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };

    // 计算模型大小
    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));

    // 设置相机位置 - 确保能看到模型
    // 由于模型在Z轴负方向，我们需要调整相机位置
    camera->SetPosition(center[0], center[1], center[2] + size * 2.0); // 从上方看
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0); // Y轴向上

    // 重置相机以确保模型完全可见
    renderer->ResetCamera();
    camera->Zoom(0.9); // 稍微缩小一点以确保完全可见

    std::cout << "模型边界: X[" << bounds[0] << ", " << bounds[1] << "] "
              << "Y[" << bounds[2] << ", " << bounds[3] << "] "
              << "Z[" << bounds[4] << ", " << bounds[5] << "]" << std::endl;
    std::cout << "模型中心: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;

    // 开始渲染和交互
    renderWindow->Render();

    std::cout << "\n程序已启动，请在3D模型上点击选择点..." << std::endl;

    renderWindowInteractor->Start();

    return EXIT_SUCCESS;
}