#include <iostream>
#include <vector>
#include <memory>
#include <thread>

// VTK头文件
#include <vtkSmartPointer.h>
#include <vtkCubeSource.h>
#include <vtkPolyDataMapper.h>
#include <vtkActor.h>
#include <vtkRenderer.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkProperty.h>
#include <vtkCamera.h>
#include <vtkLight.h>
#include <vtkCellPicker.h>
#include <vtkPointPicker.h>
#include <vtkWorldPointPicker.h>
#include <vtkPolyData.h>
#include <vtkPoints.h>
#include <vtkCellArray.h>
#include <vtkLine.h>
#include <vtkPolygon.h>
#include <vtkClipPolyData.h>
#include <vtkPlane.h>
#include <vtkImplicitBoolean.h>
#include <vtkSphere.h>
#include <vtkExtractGeometry.h>
#include <vtkDataSetMapper.h>
#include <vtkUnstructuredGrid.h>
#include <vtkIdList.h>
#include <vtkCallbackCommand.h>
#include <vtkCommand.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
#include <vtkTriangleFilter.h>
#include <vtkLinearSubdivisionFilter.h>
#include <vtkBooleanOperationPolyDataFilter.h>
#include <vtkAppendPolyData.h>
#include <vtkDelaunay2D.h>
#include <vtkPolyDataNormals.h>
#include <vtkReverseSense.h>
#include <vtkTransform.h>
#include <vtkTransformPolyDataFilter.h>
#include <vtkMath.h>
#include <vtkPlaneSource.h>
#include <vtkQuad.h>
#include <vtkSphereSource.h>
#include <vtkPLYReader.h>
#include <vtkCleanPolyData.h>
#include <vtkExtractSelection.h>
#include <vtkSelection.h>
#include <vtkSelectionNode.h>
#include <vtkIdTypeArray.h>
#include <vtkGeometryFilter.h>
#include <thread>
#include <chrono>
#include <cfloat>
#include <cmath>
#include <algorithm>
#include <fstream>
#include <vtkActor2D.h> // 添加对vtkActor2D的支持

// 操作模式枚举
enum class InteractionMode {
    NORMAL,    // 普通视图操作（旋转、缩放、平移）
    ERASE,     // 擦除模式
    DRAW       // 多边形绘制模式
};

// 自定义交互样式类
class CustomInteractorStyle : public vtkInteractorStyleTrackballCamera {
public:
    static CustomInteractorStyle* New();
    vtkTypeMacro(CustomInteractorStyle, vtkInteractorStyleTrackballCamera);

    // 构造函数
    CustomInteractorStyle() {
        mode = InteractionMode::NORMAL;
        isDrawing = false;
        drawingPoints = vtkSmartPointer<vtkPoints>::New();
        drawingLines = vtkSmartPointer<vtkCellArray>::New();
        drawingPolyData = vtkSmartPointer<vtkPolyData>::New();
        drawingMapper = vtkSmartPointer<vtkPolyDataMapper>::New();
        drawingActor = vtkSmartPointer<vtkActor>::New();

        // 设置绘制线条的外观 - 增强版本
        drawingPolyData->SetPoints(drawingPoints);
        drawingPolyData->SetLines(drawingLines);
        drawingMapper->SetInputData(drawingPolyData);
        drawingActor->SetMapper(drawingMapper);

        // 极大增强线条显示属性
        drawingActor->GetProperty()->SetColor(1.0, 0.0, 0.0); // 鲜红色
        drawingActor->GetProperty()->SetLineWidth(8.0); // 大幅增加线宽
        drawingActor->GetProperty()->SetOpacity(1.0); // 完全不透明
        drawingActor->GetProperty()->SetAmbient(1.0); // 最大化环境光
        drawingActor->GetProperty()->SetDiffuse(1.0); // 最大化漫反射
        drawingActor->GetProperty()->SetRenderLinesAsTubes(true); // 渲染为管状线条
        drawingActor->GetProperty()->SetRepresentationToWireframe(); // 确保线框渲染
        
        // 设置线条始终在最上层显示
        drawingActor->ForceOpaqueOn();
        drawingActor->GetProperty()->SetLighting(false); // 禁用光照使线条始终可见
        drawingActor->SetVisibility(true); // 确保可见性

        std::cout << "绘制actor初始化完成，设置为增强可见度的红色线条" << std::endl;

        picker = vtkSmartPointer<vtkCellPicker>::New();
        picker->SetTolerance(0.0005); // 提高拾取精度
    }

    // 设置主要的actor和renderer
    void SetMainActor(vtkActor* actor) { mainActor = actor; }
    void SetRenderer(vtkRenderer* ren) {
        renderer = ren;
        
        // 确保绘制actor被添加为最后一个actor，使其显示在其他对象之上
        renderer->RemoveActor(drawingActor); // 先移除以防重复添加
        renderer->AddActor(drawingActor);
        
        // 强制设置绘制actor的特性以确保可见性
        drawingActor->SetVisibility(true);
        drawingActor->SetPickable(false); // 使线条不可拾取，避免干扰正方体拾取
        drawingActor->GetProperty()->SetLineWidth(8.0); // 再次确保线宽够大
        drawingActor->SetPosition(0, 0, 0.001); // 轻微偏移确保在模型表面之上
        
        std::cout << "绘制actor已添加到渲染器，确保最高层级显示" << std::endl;
        
        // 立即渲染一次以确保设置生效
        if(renderer->GetRenderWindow()) {
            renderer->GetRenderWindow()->Render();
        }
    }
    void SetStatusText(vtkTextActor* text) { statusText = text; }

    // 切换操作模式
    void SetMode(InteractionMode newMode) {
        // 记录先前的模式
        InteractionMode oldMode = mode;
        mode = newMode;
        
        // 更新状态文本
        UpdateStatusText();

        // 如果从绘制模式切换到其他模式，清除绘制的路径
        if (oldMode == InteractionMode::DRAW && mode != InteractionMode::DRAW) {
            ClearDrawing();
        }
        
        // 如果切换到绘制模式，确保绘制Actor可见
        if (mode == InteractionMode::DRAW) {
            std::cout << "切换到绘制模式，初始化绘制环境..." << std::endl;
            
            // 重新初始化绘制Actor确保其可见性
            drawingActor->SetVisibility(true);
            drawingActor->GetProperty()->SetColor(1.0, 0.0, 0.0);
            drawingActor->GetProperty()->SetLineWidth(8.0);
            drawingActor->ForceOpaqueOn();
            
            // 强制渲染器将绘制Actor放在最上层
            if (renderer) {
                renderer->RemoveActor(drawingActor);
                renderer->AddActor(drawingActor);
                if (renderer->GetRenderWindow()) {
                    renderer->GetRenderWindow()->Render();
                }
            }
            
            std::cout << "绘制模式初始化完成" << std::endl;
        }
    }

    InteractionMode GetMode() const { return mode; }

private:
    InteractionMode mode;
    bool isDrawing;

    // 绘制相关
    vtkSmartPointer<vtkPoints> drawingPoints;
    vtkSmartPointer<vtkCellArray> drawingLines;
    vtkSmartPointer<vtkPolyData> drawingPolyData;
    vtkSmartPointer<vtkPolyDataMapper> drawingMapper;
    vtkSmartPointer<vtkActor> drawingActor;

    // 主要对象
    vtkActor* mainActor;
    vtkRenderer* renderer;
    vtkTextActor* statusText;
    vtkSmartPointer<vtkCellPicker> picker;

    std::vector<double> polygonPoints; // 存储多边形顶点
    std::vector<vtkSmartPointer<vtkProp>> debugSpheres; // 存储调试可视化对象(vtkProp是vtkActor和vtkTextActor的共同基类)

    // 更新状态文本
    void UpdateStatusText() {
        if (!statusText) return;

        std::string text;
        switch (mode) {
            case InteractionMode::NORMAL:
                text = "模式: 普通视图 | E-擦除, D-绘制, 鼠标-旋转/缩放/平移, ESC-退出";
                break;
            case InteractionMode::ERASE:
                text = "模式: 擦除模式 | 点击擦除区域 | N-普通, D-绘制, ESC-退出";
                break;
            case InteractionMode::DRAW:
                {
                    int pointCount = polygonPoints.size() / 3;
                    if (pointCount == 0) {
                        text = "模式: 多边形挖洞 | 左键-选择轮廓顶点 | N-普通, E-擦除, ESC-退出";
                    } else if (pointCount < 3) {
                        text = "模式: 多边形挖洞 | 已选择" + std::to_string(pointCount) + "个顶点，至少需要3个顶点形成多边形 | 左键-继续选择";
                    } else {
                        text = "模式: 多边形挖洞 | 多边形轮廓" + std::to_string(pointCount) + "个顶点 | 右键-闭合轮廓并挖洞, 左键-添加顶点";
                    }
                }
                break;
        }
        statusText->SetInput(text.c_str());
    }

    // 清除绘制内容 - 增强版本
    void ClearDrawing() {
        std::cout << "清除绘制内容..." << std::endl;

        // 重置所有绘制数据
        drawingPoints->Reset();
        drawingLines->Reset();
        drawingPolyData->SetPoints(drawingPoints);
        drawingPolyData->SetLines(drawingLines);
        polygonPoints.clear();
        isDrawing = false;

        // 清除所有调试可视化对象
        ClearDebugVisualization();
        
        // 确保更新绘制对象
        drawingPolyData->Modified();
        drawingMapper->Update();
        
        // 强制刷新绘制actor状态
        drawingActor->SetVisibility(false);
        if (renderer) {
            renderer->GetRenderWindow()->Render();
        }
        
        // 重新显示绘制actor以确保它可以接受新的绘制内容
        drawingActor->SetVisibility(true);
        
        if (renderer && renderer->GetRenderWindow()) {
            renderer->GetRenderWindow()->Render();
        }

        std::cout << "绘制内容已完全清除" << std::endl;
    }

    // 鼠标左键按下事件
    void OnLeftButtonDown() override {
        switch (mode) {
            case InteractionMode::NORMAL:
                vtkInteractorStyleTrackballCamera::OnLeftButtonDown();
                break;
            case InteractionMode::ERASE:
                HandleErase();
                break;
            case InteractionMode::DRAW:
                HandleDrawPoint();
                break;
        }
    }

    // 鼠标右键按下事件
    void OnRightButtonDown() override {
        if (mode == InteractionMode::DRAW && polygonPoints.size() >= 6) { // 至少3个点
            HandlePolygonCut();
        } else {
            vtkInteractorStyleTrackballCamera::OnRightButtonDown();
        }
    }

    // 鼠标移动事件
    void OnMouseMove() override {
        // 简化鼠标移动处理，避免API兼容性问题
        if (mode != InteractionMode::DRAW) {
            vtkInteractorStyleTrackballCamera::OnMouseMove();
        }
    }

    // 键盘事件
    void OnKeyPress() override {
        vtkRenderWindowInteractor* rwi = this->GetInteractor();
        std::string key = rwi->GetKeySym();

        std::cout << "按键检测: " << key << std::endl;

        // 处理模式切换键，完全阻止默认行为
        if (key == "n" || key == "N") {
            SetMode(InteractionMode::NORMAL);
            std::cout << "切换到普通视图模式" << std::endl;
            // 阻止事件传播
            return;
        } else if (key == "e" || key == "E") {
            SetMode(InteractionMode::ERASE);
            std::cout << "切换到擦除模式" << std::endl;
            // 完全阻止E键的默认行为，不调用父类方法
            return;
        } else if (key == "d" || key == "D") {
            SetMode(InteractionMode::DRAW);
            std::cout << "切换到绘制模式" << std::endl;
            // 阻止事件传播
            return;
        } else if (key == "Escape") {
            // 只有ESC键才退出程序
            std::cout << "ESC键按下，退出程序" << std::endl;
            rwi->ExitCallback();
            return;
        }

        // 对于其他键，调用父类方法
        vtkInteractorStyleTrackballCamera::OnKeyPress();

        // 强制重新渲染以更新状态文本
        if (renderer) {
            renderer->GetRenderWindow()->Render();
        }
    }

    // 重写OnChar方法以进一步阻止字符事件
    void OnChar() override {
        vtkRenderWindowInteractor* rwi = this->GetInteractor();
        std::string key = rwi->GetKeySym();

        // 对于我们处理的键，完全阻止字符事件
        if (key == "e" || key == "E" || key == "d" || key == "D" || key == "n" || key == "N") {
            std::cout << "阻止字符事件: " << key << std::endl;
            return;
        }

        // 其他字符事件交给父类处理
        vtkInteractorStyleTrackballCamera::OnChar();
    }

    // 处理擦除操作
    void HandleErase() {
        if (!mainActor || !renderer) return;

        int* clickPos = this->GetInteractor()->GetEventPosition();

        picker->SetTolerance(0.005);
        picker->Pick(clickPos[0], clickPos[1], 0, renderer);

        if (picker->GetCellId() != -1) {
            // 获取被选中的单元
            vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
            if (currentMapper) {
                vtkPolyData* polyData = vtkPolyData::SafeDownCast(currentMapper->GetInput());
                if (polyData) {
                    // 创建一个新的polydata，排除被选中的单元
                    vtkSmartPointer<vtkPolyData> newPolyData = vtkSmartPointer<vtkPolyData>::New();
                    vtkSmartPointer<vtkPoints> newPoints = vtkSmartPointer<vtkPoints>::New();
                    vtkSmartPointer<vtkCellArray> newPolys = vtkSmartPointer<vtkCellArray>::New();

                    // 复制点
                    newPoints->DeepCopy(polyData->GetPoints());

                    // 复制除了被选中单元之外的所有单元
                    vtkIdType numCells = polyData->GetNumberOfCells();
                    for (vtkIdType i = 0; i < numCells; i++) {
                        if (i != picker->GetCellId()) {
                            vtkCell* cell = polyData->GetCell(i);
                            newPolys->InsertNextCell(cell);
                        }
                    }

                    newPolyData->SetPoints(newPoints);
                    newPolyData->SetPolys(newPolys);

                    // 更新mapper
                    currentMapper->SetInputData(newPolyData);
                    renderer->GetRenderWindow()->Render();
                }
            }
        }
    }

    // 处理绘制点 - 增强版本
    void HandleDrawPoint() {
        if (!renderer) return;

        int* clickPos = this->GetInteractor()->GetEventPosition();
        std::cout << "绘制点击位置: (" << clickPos[0] << ", " << clickPos[1] << ")" << std::endl;

        // 确保我们处于绘制模式
        isDrawing = true;
        
        // 将屏幕坐标转换为世界坐标
        double worldPos[3];
        if (ScreenToWorld(clickPos, worldPos)) {
            std::cout << "世界坐标: (" << worldPos[0] << ", " << worldPos[1] << ", " << worldPos[2] << ")" << std::endl;

            // 增加偏移量，使绘制内容明显浮在模型表面上
            double offset = 0.02; // 更大的偏移量以增强可见性
            worldPos[0] += offset;
            worldPos[1] += offset;
            worldPos[2] += offset;

            // 添加点到绘制路径
            vtkIdType pointId = drawingPoints->InsertNextPoint(worldPos);
            polygonPoints.push_back(worldPos[0] - offset); // 存储原始坐标用于挖洞
            polygonPoints.push_back(worldPos[1] - offset);
            polygonPoints.push_back(worldPos[2] - offset);

            std::cout << "添加了第 " << (pointId + 1) << " 个点，偏移后坐标: ("
                    << worldPos[0] << ", " << worldPos[1] << ", " << worldPos[2] << ")" << std::endl;

            // 如果不是第一个点，创建线段
            if (pointId > 0) {
                vtkSmartPointer<vtkLine> line = vtkSmartPointer<vtkLine>::New();
                line->GetPointIds()->SetId(0, pointId - 1);
                line->GetPointIds()->SetId(1, pointId);
                drawingLines->InsertNextCell(line);
                std::cout << "创建了连接线段 " << (pointId - 1) << " -> " << pointId << std::endl;
            }

            // 极大增强多边形轮廓线的可视化效果
            drawingActor->SetVisibility(true);
            drawingActor->GetProperty()->SetColor(1.0, 0.2, 0.0); // 橙红色，更醒目
            drawingActor->GetProperty()->SetLineWidth(15.0); // 超粗轮廓线
            drawingActor->GetProperty()->SetOpacity(1.0);
            drawingActor->GetProperty()->SetRenderLinesAsTubes(true);
            drawingActor->GetProperty()->SetLighting(false); // 关闭光照确保可见
            drawingActor->GetProperty()->SetAmbient(1.0); // 最大环境光
            drawingActor->GetProperty()->SetDiffuse(0.0); // 关闭漫反射
            drawingActor->ForceOpaqueOn();  // 强制不透明

            // 设置更大的偏移确保轮廓线始终可见
            drawingActor->SetPosition(0, 0, 0.01);

            // 确保轮廓线在渲染堆栈的最上层
            renderer->RemoveActor(drawingActor);
            renderer->AddActor(drawingActor);

            // 设置渲染优先级
            drawingActor->SetPickable(false); // 不可拾取

            std::cout << "多边形轮廓线已更新，当前包含 " << drawingLines->GetNumberOfCells() << " 条线段" << std::endl;
            std::cout << "轮廓线设置：橙红色，线宽15，管状渲染，强制可见" << std::endl;
            
            drawingPolyData->Modified();

            // 添加更明显的调试可视化点标记
            AddDebugSphere(worldPos, pointId);

            // 更新状态文本显示当前点数
            UpdateStatusText();

            std::cout << "当前绘制数据包含 " << drawingPoints->GetNumberOfPoints() << " 个点，"
                    << drawingLines->GetNumberOfCells() << " 条线段" << std::endl;
                    
            // 确保立即渲染更新
            if(renderer->GetRenderWindow()) {
                renderer->GetRenderWindow()->Render();
            }
        } else {
            std::cout << "坐标转换失败，尝试备用方式添加点..." << std::endl;
            
            // 备用方法 - 使用屏幕中心点向前投射
            double worldPos[3] = {0, 0, 0.5}; // 默认在立方体前方添加点
            
            if (!polygonPoints.empty()) {
                // 使用最后添加的点附近位置
                worldPos[0] = polygonPoints[polygonPoints.size() - 3] + 0.2;
                worldPos[1] = polygonPoints[polygonPoints.size() - 2] + 0.1;
                worldPos[2] = polygonPoints[polygonPoints.size() - 1] + 0.1;
            }
            
            // 添加点
            vtkIdType pointId = drawingPoints->InsertNextPoint(worldPos);
            polygonPoints.push_back(worldPos[0]);
            polygonPoints.push_back(worldPos[1]);
            polygonPoints.push_back(worldPos[2]);
            
            // 与前一点连线
            if (pointId > 0) {
                vtkSmartPointer<vtkLine> line = vtkSmartPointer<vtkLine>::New();
                line->GetPointIds()->SetId(0, pointId - 1);
                line->GetPointIds()->SetId(1, pointId);
                drawingLines->InsertNextCell(line);
            }
            
            // 添加可视化小球
            AddDebugSphere(worldPos, pointId);
            drawingPolyData->Modified();
            UpdateStatusText();
            
            if(renderer->GetRenderWindow()) {
                renderer->GetRenderWindow()->Render();
            }
            
            std::cout << "使用备用方法添加了点" << std::endl;
        }
    }
    // 添加调试可视化小球 - 超强可见性版本
    void AddDebugSphere(double position[3], int pointId) {
        vtkSmartPointer<vtkSphereSource> sphereSource = vtkSmartPointer<vtkSphereSource>::New();
        sphereSource->SetCenter(position[0], position[1], position[2]);
        sphereSource->SetRadius(0.15); // 大幅增大球体半径
        sphereSource->SetPhiResolution(20);
        sphereSource->SetThetaResolution(20);

        vtkSmartPointer<vtkPolyDataMapper> sphereMapper = vtkSmartPointer<vtkPolyDataMapper>::New();
        sphereMapper->SetInputConnection(sphereSource->GetOutputPort());

        vtkSmartPointer<vtkActor> sphereActor = vtkSmartPointer<vtkActor>::New();
        sphereActor->SetMapper(sphereMapper);
        
        // 根据点ID设置不同颜色，使连续的点更容易区分
        if (pointId == 0) {
            // 第一个点使用绿色
            sphereActor->GetProperty()->SetColor(0.0, 1.0, 0.0);
        } else {
            // 其他点使用黄色
            sphereActor->GetProperty()->SetColor(1.0, 1.0, 0.0);
        }
        
        sphereActor->GetProperty()->SetOpacity(1.0); // 完全不透明
        sphereActor->GetProperty()->SetSpecular(0.6); // 增加高光
        sphereActor->GetProperty()->SetSpecularPower(30);
        sphereActor->GetProperty()->SetAmbient(0.4);
        sphereActor->GetProperty()->SetDiffuse(0.8);
        sphereActor->ForceOpaqueOn(); // 确保在深度排序中优先显示
        sphereActor->GetProperty()->SetLighting(true); // 启用光照增强立体感
        
        // 添加辅助文本标签显示点的编号
        vtkSmartPointer<vtkTextActor> textActor = vtkSmartPointer<vtkTextActor>::New();
        std::string label = std::to_string(pointId + 1); // 显示1-based索引
        textActor->SetInput(label.c_str());
        textActor->GetTextProperty()->SetColor(1.0, 1.0, 1.0); // 白色文本
        textActor->GetTextProperty()->SetFontSize(14);
        textActor->GetTextProperty()->SetBold(true);
        textActor->GetTextProperty()->SetJustificationToCentered();
        textActor->GetTextProperty()->SetVerticalJustificationToCentered();
        
        // 将球体位置从3D世界坐标转换为2D屏幕坐标以放置文本
        double displayPos[3];
        renderer->SetWorldPoint(position[0] + 0.1, position[1] + 0.1, position[2] + 0.1, 1.0);
        renderer->WorldToDisplay();
        renderer->GetDisplayPoint(displayPos);
        
        // 设置文本位置在屏幕坐标系统中
        textActor->SetPosition(displayPos[0] + 10, displayPos[1] + 10);
        
        // 存储调试对象以便后续清理
        debugSpheres.push_back(sphereActor);
        debugSpheres.push_back(textActor);
        
        // 添加到渲染器
        renderer->AddActor(sphereActor);
        renderer->AddActor2D(textActor);

        std::cout << "添加调试球体 " << (pointId + 1) << " 在位置 ("
                  << position[0] << ", " << position[1] << ", " << position[2] << ")" << std::endl;
    }

    // 清除调试可视化
    void ClearDebugVisualization() {
        // 将所有可视化对象从渲染器中移除
        for (auto& prop : debugSpheres) {
            // 根据类型分别处理2D和3D对象
            if (vtkActor2D::SafeDownCast(prop)) {
                renderer->RemoveActor2D(vtkActor2D::SafeDownCast(prop));
            } else {
                renderer->RemoveActor(prop);
            }
        }
        debugSpheres.clear();
        std::cout << "清除了所有调试可视化对象" << std::endl;
    }

    // 处理多边形切割
    void HandlePolygonCut() {
        int pointCount = polygonPoints.size() / 3;
        if (pointCount < 3 || !mainActor) {
            std::cout << "需要至少3个点才能进行挖洞操作，当前只有 " << pointCount << " 个点" << std::endl;
            return;
        }

        std::cout << "开始执行挖洞操作，使用 " << pointCount << " 个点..." << std::endl;
        
        // 记录正在进行挖洞操作，并打印详细信息以帮助诊断
        std::cout << "------- 挖洞操作详细诊断 -------" << std::endl;
        std::cout << "点数: " << pointCount << std::endl;
        
        // 打印所有点的坐标
        std::cout << "点坐标:" << std::endl;
        for (int i = 0; i < pointCount; ++i) {
            std::cout << "  点 " << (i+1) << ": (" 
                      << polygonPoints[i*3] << ", " 
                      << polygonPoints[i*3+1] << ", " 
                      << polygonPoints[i*3+2] << ")" << std::endl;
        }
        std::cout << "--------------------------------" << std::endl;

        // 闭合多边形 - 连接最后一个点到第一个点
        vtkIdType numPoints = drawingPoints->GetNumberOfPoints();
        if (numPoints >= 2) {
            // 添加闭合线段
            vtkSmartPointer<vtkLine> closingLine = vtkSmartPointer<vtkLine>::New();
            closingLine->GetPointIds()->SetId(0, numPoints - 1);
            closingLine->GetPointIds()->SetId(1, 0);
            drawingLines->InsertNextCell(closingLine);
            drawingPolyData->Modified();

            // 强化闭合轮廓的可视化效果
            drawingActor->GetProperty()->SetLineWidth(15.0);  // 使用超粗线宽突出闭合轮廓
            drawingActor->GetProperty()->SetColor(1.0, 0.2, 0.0); // 橙红色，更醒目
            drawingActor->ForceOpaqueOn(); // 强制不透明渲染
            drawingActor->GetProperty()->SetLighting(false); // 禁用光照，确保清晰可见
            drawingActor->SetVisibility(true); // 确保可见
            drawingActor->GetProperty()->SetRenderLinesAsTubes(true); // 以管道形式渲染线条
            drawingActor->GetProperty()->SetOpacity(1.0); // 完全不透明

            // 设置特殊渲染属性确保轮廓线始终可见
            drawingActor->SetPosition(0, 0, 0.005); // 轻微向前偏移
            
            // 确保绘制actor在最前面显示
            if (renderer) {
                renderer->RemoveActor(drawingActor);
                renderer->AddActor(drawingActor);
            }
            
            // 显示闭合的轮廓并短暂延时，让用户能看到
            if (renderer && renderer->GetRenderWindow()) {
                renderer->GetRenderWindow()->Render();
                std::cout << "显示闭合轮廓..." << std::endl;
                
                // 添加小延时以确保用户能看到闭合轮廓
                std::this_thread::sleep_for(std::chrono::milliseconds(800));
            }
        }

        // 执行切割操作 - 使用安全方法
        std::cout << "开始执行安全挖洞操作..." << std::endl;

        // 验证当前几何体状态
        vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
        if (currentMapper) {
            vtkPolyData* currentGeometry = vtkPolyData::SafeDownCast(currentMapper->GetInput());
            if (currentGeometry) {
                std::cout << "挖洞前几何体状态：" << std::endl;
                std::cout << "  顶点数: " << currentGeometry->GetNumberOfPoints() << std::endl;
                std::cout << "  单元数: " << currentGeometry->GetNumberOfCells() << std::endl;
            }
        }

        bool success = false;
        try {
            success = PerformSafeHoleCutting();
        } catch (const std::exception& e) {
            std::cout << "挖洞操作异常: " << e.what() << std::endl;
            success = false;
        } catch (...) {
            std::cout << "挖洞操作发生未知异常" << std::endl;
            success = false;
        }

        if (success) {
            std::cout << "挖洞操作成功完成！" << std::endl;

            // 验证挖洞后几何体状态
            vtkPolyDataMapper* updatedMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
            if (updatedMapper) {
                vtkPolyData* newGeometry = vtkPolyData::SafeDownCast(updatedMapper->GetInput());
                if (newGeometry) {
                    std::cout << "挖洞后几何体状态验证：" << std::endl;
                    std::cout << "  顶点数: " << newGeometry->GetNumberOfPoints() << std::endl;
                    std::cout << "  单元数: " << newGeometry->GetNumberOfCells() << std::endl;

                    // 验证几何体数据完整性
                    bool isValid = true;
                    if (newGeometry->GetNumberOfPoints() == 0 || newGeometry->GetNumberOfCells() == 0) {
                        isValid = false;
                        std::cout << "警告：几何体数据可能无效" << std::endl;
                    }

                    if (isValid) {
                        std::cout << "几何体数据验证通过，可安全进行下次操作" << std::endl;
                    } else {
                        std::cout << "几何体数据验证失败，建议重新启动应用程序" << std::endl;
                    }
                }
            }
        } else {
            std::cout << "挖洞操作失败，但程序继续运行" << std::endl;
        }

        // 清除绘制内容
        ClearDrawing();
    }

    // 判断点是否在多边形内部 - 改进的射线投射算法
    bool IsPointInPolygon(double point[3], const std::vector<double>& polygonPoints) {
        int pointCount = polygonPoints.size() / 3;
        if (pointCount < 3) return false;

        // 计算多边形的中心点和法向量
        double polygonCenter[3] = {0, 0, 0};
        double normal[3] = {0, 0, 1};

        // 计算中心点
        for (int i = 0; i < pointCount; i++) {
            polygonCenter[0] += polygonPoints[i * 3];
            polygonCenter[1] += polygonPoints[i * 3 + 1];
            polygonCenter[2] += polygonPoints[i * 3 + 2];
        }
        polygonCenter[0] /= pointCount;
        polygonCenter[1] /= pointCount;
        polygonCenter[2] /= pointCount;

        // 计算法向量
        CalculatePolygonNormal(polygonPoints, normal);

        // 将测试点投影到多边形平面上
        double vectorToPoint[3] = {
            point[0] - polygonCenter[0],
            point[1] - polygonCenter[1],
            point[2] - polygonCenter[2]
        };

        // 计算点到平面的距离
        double distanceToPlane = vectorToPoint[0] * normal[0] +
                                vectorToPoint[1] * normal[1] +
                                vectorToPoint[2] * normal[2];

        // 将点投影到多边形平面
        double projectedPoint[3] = {
            point[0] - distanceToPlane * normal[0],
            point[1] - distanceToPlane * normal[1],
            point[2] - distanceToPlane * normal[2]
        };

        // 选择最佳的2D投影平面
        int projectionPlane = 2; // 默认投影到XY平面
        double maxComponent = fabs(normal[2]);

        if (fabs(normal[0]) > maxComponent) {
            projectionPlane = 0; // 投影到YZ平面
            maxComponent = fabs(normal[0]);
        }
        if (fabs(normal[1]) > maxComponent) {
            projectionPlane = 1; // 投影到XZ平面
        }

        // 获取2D投影坐标的函数
        auto getProjected2D = [projectionPlane](double p[3]) -> std::pair<double, double> {
            switch (projectionPlane) {
                case 0: return {p[1], p[2]}; // YZ平面
                case 1: return {p[0], p[2]}; // XZ平面
                default: return {p[0], p[1]}; // XY平面
            }
        };

        auto testPoint2D = getProjected2D(projectedPoint);

        // 改进的射线投射算法
        int intersectionCount = 0;
        const double epsilon = 1e-10; // 数值精度容差

        for (int i = 0; i < pointCount; i++) {
            int next = (i + 1) % pointCount;

            double p1[3] = {polygonPoints[i * 3], polygonPoints[i * 3 + 1], polygonPoints[i * 3 + 2]};
            double p2[3] = {polygonPoints[next * 3], polygonPoints[next * 3 + 1], polygonPoints[next * 3 + 2]};

            auto poly1_2D = getProjected2D(p1);
            auto poly2_2D = getProjected2D(p2);

            // 检查射线与线段的交点（改进版本）
            double y1 = poly1_2D.second;
            double y2 = poly2_2D.second;
            double x1 = poly1_2D.first;
            double x2 = poly2_2D.first;
            double testY = testPoint2D.second;
            double testX = testPoint2D.first;

            // 检查线段是否跨越测试点的水平线
            if (((y1 > testY) != (y2 > testY)) &&
                (fabs(y2 - y1) > epsilon)) { // 避免除零

                // 计算交点的X坐标
                double intersectionX = x1 + (x2 - x1) * (testY - y1) / (y2 - y1);

                // 如果交点在测试点右侧，计数加一
                if (intersectionX > testX) {
                    intersectionCount++;
                }
            }
        }

        // 奇数个交点表示点在多边形内部
        bool isInside = (intersectionCount % 2) == 1;

        return isInside;
    }

    // 计算多边形法向量
    void CalculatePolygonNormal(const std::vector<double>& polygonPoints, double normal[3]) {
        int pointCount = polygonPoints.size() / 3;
        if (pointCount < 3) {
            normal[0] = 0; normal[1] = 0; normal[2] = 1;
            return;
        }

        // 使用前三个点计算法向量
        double p0[3] = {polygonPoints[0], polygonPoints[1], polygonPoints[2]};
        double p1[3] = {polygonPoints[3], polygonPoints[4], polygonPoints[5]};
        double p2[3] = {polygonPoints[6], polygonPoints[7], polygonPoints[8]};

        double v1[3] = {p1[0] - p0[0], p1[1] - p0[1], p1[2] - p0[2]};
        double v2[3] = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};

        // 叉积
        normal[0] = v1[1] * v2[2] - v1[2] * v2[1];
        normal[1] = v1[2] * v2[0] - v1[0] * v2[2];
        normal[2] = v1[0] * v2[1] - v1[1] * v2[0];

        // 归一化
        double length = sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
        if (length > 0.0001) {
            normal[0] /= length;
            normal[1] /= length;
            normal[2] /= length;
        } else {
            normal[0] = 0; normal[1] = 0; normal[2] = 1;
        }
    }

    // 安全的挖洞方法 - 基于多边形轮廓的精确单面挖洞
    bool PerformSafeHoleCutting() {
        std::cout << "=== 执行安全的单面多边形挖洞操作 ===" << std::endl;

        try {
            // 验证多边形轮廓
            int pointCount = polygonPoints.size() / 3;
            if (pointCount < 3) {
                std::cout << "错误：多边形轮廓点数不足（需要至少3个点）" << std::endl;
                return false;
            }

            std::cout << "多边形轮廓包含 " << pointCount << " 个顶点" << std::endl;

            // 获取当前几何体
            vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
            if (!currentMapper) {
                std::cout << "错误：无法获取mapper" << std::endl;
                return false;
            }

            vtkPolyData* currentGeometry = vtkPolyData::SafeDownCast(currentMapper->GetInput());
            if (!currentGeometry) {
                std::cout << "错误：无法获取几何数据" << std::endl;
                return false;
            }

            std::cout << "当前几何体：" << currentGeometry->GetNumberOfPoints() << " 个顶点，"
                      << currentGeometry->GetNumberOfCells() << " 个单元" << std::endl;

            // 第一步：计算多边形轮廓的平面和法向量
            double polygonCenter[3] = {0, 0, 0};
            double polygonNormal[3] = {0, 0, 1};

            // 计算轮廓中心点
            for (int i = 0; i < pointCount; i++) {
                polygonCenter[0] += polygonPoints[i * 3];
                polygonCenter[1] += polygonPoints[i * 3 + 1];
                polygonCenter[2] += polygonPoints[i * 3 + 2];
            }
            polygonCenter[0] /= pointCount;
            polygonCenter[1] /= pointCount;
            polygonCenter[2] /= pointCount;

            // 计算轮廓法向量（使用前三个点）
            if (pointCount >= 3) {
                double p0[3] = {polygonPoints[0], polygonPoints[1], polygonPoints[2]};
                double p1[3] = {polygonPoints[3], polygonPoints[4], polygonPoints[5]};
                double p2[3] = {polygonPoints[6], polygonPoints[7], polygonPoints[8]};

                double v1[3] = {p1[0] - p0[0], p1[1] - p0[1], p1[2] - p0[2]};
                double v2[3] = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};

                // 叉积计算法向量
                polygonNormal[0] = v1[1] * v2[2] - v1[2] * v2[1];
                polygonNormal[1] = v1[2] * v2[0] - v1[0] * v2[2];
                polygonNormal[2] = v1[0] * v2[1] - v1[1] * v2[0];

                // 归一化
                double length = sqrt(polygonNormal[0]*polygonNormal[0] +
                                   polygonNormal[1]*polygonNormal[1] +
                                   polygonNormal[2]*polygonNormal[2]);
                if (length > 0.0001) {
                    polygonNormal[0] /= length;
                    polygonNormal[1] /= length;
                    polygonNormal[2] /= length;
                }
            }

            std::cout << "轮廓中心: (" << polygonCenter[0] << ", " << polygonCenter[1] << ", " << polygonCenter[2] << ")" << std::endl;
            std::cout << "轮廓法向量: (" << polygonNormal[0] << ", " << polygonNormal[1] << ", " << polygonNormal[2] << ")" << std::endl;

            // 计算轮廓的大致尺寸用于调试
            double polygonRadius = 0;
            for (int k = 0; k < pointCount; k++) {
                double dx = polygonPoints[k * 3] - polygonCenter[0];
                double dy = polygonPoints[k * 3 + 1] - polygonCenter[1];
                double dz = polygonPoints[k * 3 + 2] - polygonCenter[2];
                polygonRadius = std::max(polygonRadius, sqrt(dx*dx + dy*dy + dz*dz));
            }
            std::cout << "轮廓半径: " << polygonRadius << std::endl;

            // 第二步：确定挖洞深度限制（只挖表面层）
            // 根据模型大小动态计算合适的挖洞深度
            double bounds[6];
            currentGeometry->GetBounds(bounds);
            double modelSize = std::max({
                bounds[1] - bounds[0],
                bounds[3] - bounds[2],
                bounds[5] - bounds[4]
            });

            // 设置挖洞深度为模型尺寸的5-10%，确保有明显的视觉效果
            double maxHoleDepth = modelSize * 0.08; // 增加到8%的模型尺寸
            std::cout << "模型尺寸: " << modelSize << std::endl;
            std::cout << "设置最大挖洞深度: " << maxHoleDepth << " (模型尺寸的8%)" << std::endl;

            // 创建完全新的几何体，使用更安全的重建方法
            vtkSmartPointer<vtkPolyData> newGeometry = vtkSmartPointer<vtkPolyData>::New();

            // 第三步：收集所有要保留的单元（使用单面挖洞算法）
            std::vector<vtkIdType> keptCellIds;
            int removedCells = 0;
            vtkIdType numCells = currentGeometry->GetNumberOfCells();

            std::cout << "第三步：使用单面挖洞算法识别要保留的单元..." << std::endl;

            for (vtkIdType i = 0; i < numCells; i++) {
                vtkCell* cell = currentGeometry->GetCell(i);
                if (!cell) continue;

                // 计算单元中心点
                double cellCenter[3] = {0, 0, 0};
                int numCellPoints = cell->GetNumberOfPoints();

                for (int j = 0; j < numCellPoints; j++) {
                    double point[3];
                    currentGeometry->GetPoint(cell->GetPointId(j), point);
                    cellCenter[0] += point[0];
                    cellCenter[1] += point[1];
                    cellCenter[2] += point[2];
                }
                cellCenter[0] /= numCellPoints;
                cellCenter[1] /= numCellPoints;
                cellCenter[2] /= numCellPoints;

                // 关键改进：多层次检查单元是否应该被移除

                // 1. 首先检查单元是否在多边形轮廓内（这是主要条件）
                bool isInsidePolygon = IsPointInPolygon(cellCenter, polygonPoints);

                // 2. 如果在轮廓内，再检查是否在合适的深度范围内
                bool shouldRemove = false;
                if (isInsidePolygon) {
                    // 计算单元中心到轮廓平面的距离
                    double distanceToPlane = fabs(
                        polygonNormal[0] * (cellCenter[0] - polygonCenter[0]) +
                        polygonNormal[1] * (cellCenter[1] - polygonCenter[1]) +
                        polygonNormal[2] * (cellCenter[2] - polygonCenter[2])
                    );

                    // 使用更宽松的深度检查，允许更大范围的表面单元被移除
                    bool isInDepthRange = (distanceToPlane <= maxHoleDepth);

                    // 3. 额外检查：如果单元非常接近轮廓中心，即使稍微超出深度限制也移除
                    double distanceToCenter = sqrt(
                        pow(cellCenter[0] - polygonCenter[0], 2) +
                        pow(cellCenter[1] - polygonCenter[1], 2) +
                        pow(cellCenter[2] - polygonCenter[2], 2)
                    );

                    // 计算轮廓的大致半径
                    double polygonRadius = 0;
                    for (int k = 0; k < pointCount; k++) {
                        double dx = polygonPoints[k * 3] - polygonCenter[0];
                        double dy = polygonPoints[k * 3 + 1] - polygonCenter[1];
                        double dz = polygonPoints[k * 3 + 2] - polygonCenter[2];
                        polygonRadius = std::max(polygonRadius, sqrt(dx*dx + dy*dy + dz*dz));
                    }

                    bool isNearCenter = (distanceToCenter <= polygonRadius * 1.2); // 120%的轮廓半径内

                    // 如果满足深度条件或者非常接近轮廓中心，则移除
                    shouldRemove = isInDepthRange || isNearCenter;
                }

                if (shouldRemove) {
                    removedCells++;
                    // 不添加到保留列表，即移除此单元
                } else {
                    keptCellIds.push_back(i);
                }

                // 进度输出和调试信息
                if ((i + 1) % 5000 == 0) {
                    std::cout << "已分析 " << (i + 1) << "/" << numCells << " 个单元，当前已移除 " << removedCells << " 个单元" << std::endl;
                }

                // 详细调试：记录前几个被移除的单元信息
                if (shouldRemove && removedCells <= 10) {
                    std::cout << "移除单元 " << i << ": 中心(" << cellCenter[0] << ", " << cellCenter[1] << ", " << cellCenter[2] << ")" << std::endl;
                }
            }

            std::cout << "第四步：使用VTK提取滤波器重建几何体..." << std::endl;

            // 使用VTK的ExtractCells滤波器，这是更安全的方法
            vtkSmartPointer<vtkIdTypeArray> cellIds = vtkSmartPointer<vtkIdTypeArray>::New();
            cellIds->SetNumberOfTuples(keptCellIds.size());
            for (size_t i = 0; i < keptCellIds.size(); i++) {
                cellIds->SetValue(i, keptCellIds[i]);
            }

            vtkSmartPointer<vtkSelectionNode> selectionNode = vtkSmartPointer<vtkSelectionNode>::New();
            selectionNode->SetFieldType(vtkSelectionNode::CELL);
            selectionNode->SetContentType(vtkSelectionNode::INDICES);
            selectionNode->SetSelectionList(cellIds);

            vtkSmartPointer<vtkSelection> selection = vtkSmartPointer<vtkSelection>::New();
            selection->AddNode(selectionNode);

            vtkSmartPointer<vtkExtractSelection> extractSelection = vtkSmartPointer<vtkExtractSelection>::New();
            extractSelection->SetInputData(0, currentGeometry);
            extractSelection->SetInputData(1, selection);
            extractSelection->Update();

            // 转换为PolyData
            vtkSmartPointer<vtkGeometryFilter> geometryFilter = vtkSmartPointer<vtkGeometryFilter>::New();
            geometryFilter->SetInputConnection(extractSelection->GetOutputPort());
            geometryFilter->Update();

            newGeometry->DeepCopy(geometryFilter->GetOutput());

            int keptCells = keptCellIds.size();

            std::cout << "单面挖洞几何体重建完成：" << std::endl;
            std::cout << "  移除了 " << removedCells << " 个表面单元" << std::endl;
            std::cout << "  保留了 " << keptCells << " 个单元" << std::endl;
            std::cout << "  新几何体包含 " << newGeometry->GetNumberOfPoints() << " 个顶点" << std::endl;
            std::cout << "  新几何体包含 " << newGeometry->GetNumberOfCells() << " 个单元" << std::endl;

            if (removedCells > 0 && keptCells > 0) {
                // 验证新几何体的完整性
                if (newGeometry->GetNumberOfCells() == 0 || newGeometry->GetNumberOfPoints() == 0) {
                    std::cout << "错误：新几何体没有有效数据" << std::endl;
                    return false;
                }

                // 强制清理和重建几何体数据结构
                std::cout << "第五步：清理和验证几何体数据..." << std::endl;
                vtkSmartPointer<vtkCleanPolyData> cleanFilter = vtkSmartPointer<vtkCleanPolyData>::New();
                cleanFilter->SetInputData(newGeometry);
                cleanFilter->SetTolerance(0.0);
                cleanFilter->Update();

                vtkSmartPointer<vtkPolyData> cleanedGeometry = cleanFilter->GetOutput();

                std::cout << "清理后几何体：" << std::endl;
                std::cout << "  顶点数: " << cleanedGeometry->GetNumberOfPoints() << std::endl;
                std::cout << "  单元数: " << cleanedGeometry->GetNumberOfCells() << std::endl;

                // 创建全新的mapper以确保完全更新
                std::cout << "第六步：创建新的mapper..." << std::endl;
                vtkSmartPointer<vtkPolyDataMapper> newMapper = vtkSmartPointer<vtkPolyDataMapper>::New();
                newMapper->SetInputData(cleanedGeometry);
                newMapper->Update();

                // 更新actor的mapper
                mainActor->SetMapper(newMapper);

                // 重新渲染
                if (renderer && renderer->GetRenderWindow()) {
                    renderer->GetRenderWindow()->Render();
                    std::cout << "渲染更新完成" << std::endl;
                }

                std::cout << "单面挖洞操作完全成功！只在选择表面创建了凹陷，未穿透模型" << std::endl;
                return true;
            } else {
                std::cout << "警告：没有移除任何表面单元或没有保留任何单元，尝试调整挖洞参数" << std::endl;

                // 如果没有移除单元，可能是深度限制太严格，尝试增加深度
                if (removedCells == 0) {
                    std::cout << "提示：可能需要增加挖洞深度限制或调整轮廓选择" << std::endl;
                }
                return false;
            }

        } catch (const std::exception& e) {
            std::cout << "安全挖洞操作异常：" << e.what() << std::endl;
            return false;
        } catch (...) {
            std::cout << "安全挖洞操作发生未知异常" << std::endl;
            return false;
        }
    }

    // 执行多边形切割 - 完整实现
    bool PerformPolygonCut() {
        try {
            // 1. 创建多边形轮廓
            vtkSmartPointer<vtkPolygon> polygon = CreatePolygonFromPoints();
            if (!polygon) {
                std::cout << "创建多边形失败" << std::endl;
                return false;
            }

            // 2. 创建挖洞几何体
            vtkSmartPointer<vtkPolyData> holeGeometry = CreateHoleGeometry(polygon);
            if (!holeGeometry) {
                std::cout << "创建挖洞几何体失败" << std::endl;
                return false;
            }

            // 3. 执行布尔运算
            return PerformBooleanOperation(holeGeometry);

        } catch (const std::exception& e) {
            std::cout << "挖洞操作异常: " << e.what() << std::endl;
            return false;
        }
    }

    // 从选择的点创建多边形
    vtkSmartPointer<vtkPolygon> CreatePolygonFromPoints() {
        int pointCount = polygonPoints.size() / 3;
        if (pointCount < 3) return nullptr;

        vtkSmartPointer<vtkPolygon> polygon = vtkSmartPointer<vtkPolygon>::New();
        polygon->GetPointIds()->SetNumberOfIds(pointCount);

        vtkSmartPointer<vtkPoints> points = vtkSmartPointer<vtkPoints>::New();
        for (int i = 0; i < pointCount; i++) {
            double point[3] = {
                polygonPoints[i * 3],
                polygonPoints[i * 3 + 1],
                polygonPoints[i * 3 + 2]
            };
            points->InsertNextPoint(point);
            polygon->GetPointIds()->SetId(i, i);
        }

        std::cout << "创建了包含 " << pointCount << " 个顶点的多边形" << std::endl;
        return polygon;
    }

    // 创建挖洞几何体
    vtkSmartPointer<vtkPolyData> CreateHoleGeometry(vtkSmartPointer<vtkPolygon> polygon) {
        int pointCount = polygonPoints.size() / 3;

        // 创建多边形的点集
        vtkSmartPointer<vtkPoints> points = vtkSmartPointer<vtkPoints>::New();
        for (int i = 0; i < pointCount; i++) {
            double point[3] = {
                polygonPoints[i * 3],
                polygonPoints[i * 3 + 1],
                polygonPoints[i * 3 + 2]
            };
            points->InsertNextPoint(point);
        }

        // 创建多边形单元
        vtkSmartPointer<vtkCellArray> polygons = vtkSmartPointer<vtkCellArray>::New();
        polygons->InsertNextCell(polygon);

        // 创建2D多边形数据
        vtkSmartPointer<vtkPolyData> polygonData = vtkSmartPointer<vtkPolyData>::New();
        polygonData->SetPoints(points);
        polygonData->SetPolys(polygons);

        // 使用Delaunay三角化填充多边形内部
        vtkSmartPointer<vtkDelaunay2D> delaunay = vtkSmartPointer<vtkDelaunay2D>::New();
        delaunay->SetInputData(polygonData);
        // 增强三角化参数以提高精度
        delaunay->SetTolerance(0.0001);
        delaunay->SetAlpha(0.1);
        delaunay->SetOffset(1.0);
        delaunay->Update();

        vtkSmartPointer<vtkPolyData> triangulatedPoly = delaunay->GetOutput();

        // 拉伸多边形创建3D挖洞体
        vtkSmartPointer<vtkPolyData> extrudedHole = ExtrudePolygon(triangulatedPoly);

        std::cout << "创建挖洞几何体，包含 " << extrudedHole->GetNumberOfCells() << " 个单元" << std::endl;
        return extrudedHole;
    }
    // 拉伸多边形创建3D体 - 健壮版本
    vtkSmartPointer<vtkPolyData> ExtrudePolygon(vtkSmartPointer<vtkPolyData> polygon2D) {
        std::cout << "开始创建拉伸几何体..." << std::endl;

        // 首先验证输入多边形的有效性
        if (!polygon2D || polygon2D->GetNumberOfPoints() < 3) {
            std::cout << "错误：无效的多边形输入，点数不足" << std::endl;
            return nullptr;
        }

        // 计算多边形的中心和法向量
        double center[3] = {0, 0, 0};
        double normal[3] = {0, 0, 1}; // 默认法向量
        CalculatePolygonCenterAndNormal(polygon2D, center, normal);

        // 标准化法向量
        double length = sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
        if (length > 0.0001) { // 避免除以零
            normal[0] /= length;
            normal[1] /= length;
            normal[2] /= length;
        } else {
            // 如果无法计算法向量，使用默认值
            normal[0] = 0;
            normal[1] = 0;
            normal[2] = 1;
        }

        std::cout << "多边形中心: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
        std::cout << "多边形法向量(标准化): (" << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;

        // 创建超大的拉伸体，确保与正方体无论如何都会相交
        double extrusionDepth = 5.0; // 增加拉伸深度到5.0单位，确保穿透整个正方体

        vtkSmartPointer<vtkAppendPolyData> appendFilter = vtkSmartPointer<vtkAppendPolyData>::New();

        // 前面（向外拉伸）
        vtkSmartPointer<vtkTransform> frontTransform = vtkSmartPointer<vtkTransform>::New();
        frontTransform->Translate(normal[0] * extrusionDepth/2,
                                 normal[1] * extrusionDepth/2,
                                 normal[2] * extrusionDepth/2);

        vtkSmartPointer<vtkTransformPolyDataFilter> frontFilter = vtkSmartPointer<vtkTransformPolyDataFilter>::New();
        frontFilter->SetInputData(polygon2D);
        frontFilter->SetTransform(frontTransform);
        frontFilter->Update();

        appendFilter->AddInputConnection(frontFilter->GetOutputPort());

        // 后面（向内拉伸）
        vtkSmartPointer<vtkTransform> backTransform = vtkSmartPointer<vtkTransform>::New();
        backTransform->Translate(-normal[0] * extrusionDepth/2,
                                -normal[1] * extrusionDepth/2,
                                -normal[2] * extrusionDepth/2);

        vtkSmartPointer<vtkTransformPolyDataFilter> backFilter = vtkSmartPointer<vtkTransformPolyDataFilter>::New();
        backFilter->SetInputData(polygon2D);
        backFilter->SetTransform(backTransform);
        backFilter->Update();

        // 反转后面的法向量
        vtkSmartPointer<vtkReverseSense> reverseFilter = vtkSmartPointer<vtkReverseSense>::New();
        reverseFilter->SetInputConnection(backFilter->GetOutputPort());
        reverseFilter->Update();

        appendFilter->AddInputConnection(reverseFilter->GetOutputPort());

        // 创建侧面
        CreateImprovedSideWalls(polygon2D, normal, extrusionDepth, appendFilter);

        appendFilter->Update();

        // 计算法向量
        vtkSmartPointer<vtkPolyDataNormals> normalFilter = vtkSmartPointer<vtkPolyDataNormals>::New();
        normalFilter->SetInputConnection(appendFilter->GetOutputPort());
        normalFilter->ComputePointNormalsOn();
        normalFilter->ComputeCellNormalsOn();
        normalFilter->Update();

        vtkSmartPointer<vtkPolyData> result = normalFilter->GetOutput();
        std::cout << "拉伸几何体创建完成，包含 " << result->GetNumberOfCells() << " 个单元" << std::endl;

        return result;
    }

    // 计算多边形中心和法向量
    void CalculatePolygonCenterAndNormal(vtkSmartPointer<vtkPolyData> polygon2D, double center[3], double normal[3]) {
        vtkPoints* points = polygon2D->GetPoints();
        int numPoints = points->GetNumberOfPoints();

        // 计算中心点
        center[0] = center[1] = center[2] = 0.0;
        for (int i = 0; i < numPoints; i++) {
            double point[3];
            points->GetPoint(i, point);
            center[0] += point[0];
            center[1] += point[1];
            center[2] += point[2];
        }
        center[0] /= numPoints;
        center[1] /= numPoints;
        center[2] /= numPoints;

        // 计算法向量（使用前三个点）
        if (numPoints >= 3) {
            double p0[3], p1[3], p2[3];
            points->GetPoint(0, p0);
            points->GetPoint(1, p1);
            points->GetPoint(2, p2);

            double v1[3] = {p1[0] - p0[0], p1[1] - p0[1], p1[2] - p0[2]};
            double v2[3] = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};

            // 叉积计算法向量
            normal[0] = v1[1] * v2[2] - v1[2] * v2[1];
            normal[1] = v1[2] * v2[0] - v1[0] * v2[2];
            normal[2] = v1[0] * v2[1] - v1[1] * v2[0];

            // 归一化
            double length = sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
            if (length > 0) {
                normal[0] /= length;
                normal[1] /= length;
                normal[2] /= length;
            }
        }
    }

    // 创建改进的侧壁
    void CreateImprovedSideWalls(vtkSmartPointer<vtkPolyData> polygon2D, double normal[3], double depth, vtkSmartPointer<vtkAppendPolyData> appendFilter) {
        vtkPoints* points = polygon2D->GetPoints();
        int numPoints = points->GetNumberOfPoints();

        std::cout << "创建侧壁，多边形有 " << numPoints << " 个顶点" << std::endl;

        // 为侧壁创建点和面
        vtkSmartPointer<vtkPoints> sidePoints = vtkSmartPointer<vtkPoints>::New();
        vtkSmartPointer<vtkCellArray> sideCells = vtkSmartPointer<vtkCellArray>::New();

        // 添加前后两层的点
        for (int i = 0; i < numPoints; i++) {
            double point[3];
            points->GetPoint(i, point);

            // 前层点（向外）
            double frontPoint[3] = {
                point[0] + normal[0] * depth/2,
                point[1] + normal[1] * depth/2,
                point[2] + normal[2] * depth/2
            };
            sidePoints->InsertNextPoint(frontPoint);

            // 后层点（向内）
            double backPoint[3] = {
                point[0] - normal[0] * depth/2,
                point[1] - normal[1] * depth/2,
                point[2] - normal[2] * depth/2
            };
            sidePoints->InsertNextPoint(backPoint);
        }

        // 创建侧面的四边形
        for (int i = 0; i < numPoints; i++) {
            int next = (i + 1) % numPoints;

            vtkSmartPointer<vtkQuad> quad = vtkSmartPointer<vtkQuad>::New();
            quad->GetPointIds()->SetId(0, i * 2);         // 当前点前层
            quad->GetPointIds()->SetId(1, next * 2);      // 下一点前层
            quad->GetPointIds()->SetId(2, next * 2 + 1);  // 下一点后层
            quad->GetPointIds()->SetId(3, i * 2 + 1);     // 当前点后层

            sideCells->InsertNextCell(quad);
        }

        vtkSmartPointer<vtkPolyData> sideWalls = vtkSmartPointer<vtkPolyData>::New();
        sideWalls->SetPoints(sidePoints);
        sideWalls->SetPolys(sideCells);

        std::cout << "侧壁创建完成，包含 " << sideCells->GetNumberOfCells() << " 个四边形" << std::endl;

        appendFilter->AddInputData(sideWalls);
    }

    // 验证几何体相交 - 增强版本
    bool ValidateGeometryIntersection(vtkSmartPointer<vtkPolyData> cubeGeometry, vtkSmartPointer<vtkPolyData> holeGeometry) {
        // 验证输入
        if (!cubeGeometry || !holeGeometry) {
            std::cout << "错误：无效的几何体参数" << std::endl;
            return false;
        }
        
        if (cubeGeometry->GetNumberOfPoints() == 0 || holeGeometry->GetNumberOfPoints() == 0) {
            std::cout << "警告：几何体没有点" << std::endl;
            return false;
        }

        // 获取边界框
        double cubeBounds[6], holeBounds[6];
        cubeGeometry->GetBounds(cubeBounds);
        holeGeometry->GetBounds(holeBounds);

        std::cout << "正方体边界: [" << cubeBounds[0] << ", " << cubeBounds[1] << "], ["
                  << cubeBounds[2] << ", " << cubeBounds[3] << "], ["
                  << cubeBounds[4] << ", " << cubeBounds[5] << "]" << std::endl;

        std::cout << "挖洞体边界: [" << holeBounds[0] << ", " << holeBounds[1] << "], ["
                  << holeBounds[2] << ", " << holeBounds[3] << "], ["
                  << holeBounds[4] << ", " << holeBounds[5] << "]" << std::endl;

        // 计算重叠区域（如果存在）
        double overlapXmin = std::max(cubeBounds[0], holeBounds[0]);
        double overlapXmax = std::min(cubeBounds[1], holeBounds[1]);
        double overlapYmin = std::max(cubeBounds[2], holeBounds[2]);
        double overlapYmax = std::min(cubeBounds[3], holeBounds[3]);
        double overlapZmin = std::max(cubeBounds[4], holeBounds[4]);
        double overlapZmax = std::min(cubeBounds[5], holeBounds[5]);
        
        // 检查边界框是否相交
        bool intersects = (overlapXmax > overlapXmin && 
                           overlapYmax > overlapYmin && 
                           overlapZmax > overlapZmin);
                           
        if (intersects) {
            // 计算重叠体积和百分比
            double overlapVolume = (overlapXmax - overlapXmin) * 
                                  (overlapYmax - overlapYmin) * 
                                  (overlapZmax - overlapZmin);
                                  
            double cubeVolume = (cubeBounds[1] - cubeBounds[0]) * 
                               (cubeBounds[3] - cubeBounds[2]) * 
                               (cubeBounds[5] - cubeBounds[4]);
                               
            double overlapPercent = (overlapVolume / cubeVolume) * 100.0;
            
            std::cout << "几何体边界框相交检查: 相交" << std::endl;
            std::cout << "重叠体积: " << overlapVolume << ", 占正方体体积的 " 
                      << overlapPercent << "%" << std::endl;
            
            // 只有当重叠区域足够大时才返回true
            if (overlapPercent > 1.0) {
                return true;
            } else {
                std::cout << "警告：重叠区域太小（<1%），可能导致布尔运算失败" << std::endl;
                return false;
            }
        } else {
            std::cout << "几何体边界框相交检查: 不相交" << std::endl;
            return false;
        }
    }

          // 执行挖洞操作 - 超强健壮版本
    bool PerformBooleanOperation(vtkSmartPointer<vtkPolyData> holeGeometry) {
        // 添加详细诊断信息
        std::cout << "\n==== 开始执行挖洞操作 - 安全模式 ====\n" << std::endl;
        
        // 验证输入参数
        if (!holeGeometry) {
            std::cout << "错误：挖洞几何体为空，直接使用备用方法" << std::endl;
            // 即使几何体为空也尝试继续执行
        }

        // 获取当前正方体的几何数据
        vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
        if (!currentMapper) {
            std::cout << "错误：无法获取当前几何数据映射器" << std::endl;
            return false;
        }

        vtkPolyData* currentGeometry = vtkPolyData::SafeDownCast(currentMapper->GetInput());
        if (!currentGeometry) {
            std::cout << "错误：无法获取当前几何数据" << std::endl;
            return false;
        }

        std::cout << "当前几何体状态: " 
                  << "点数=" << currentGeometry->GetNumberOfPoints() 
                  << ", 单元数=" << currentGeometry->GetNumberOfCells() << std::endl;
        
        if (holeGeometry) {
            std::cout << "挖洞几何体状态: " 
                      << "点数=" << holeGeometry->GetNumberOfPoints() 
                      << ", 单元数=" << holeGeometry->GetNumberOfCells() << std::endl;
        }

        // 始终直接使用备用挖洞方法，完全跳过VTK的布尔运算
        std::cout << "使用增强的安全挖洞方法..." << std::endl;

        // 所有挖洞操作都使用备用方法，完全避开VTK的布尔运算过滤器的问题
        std::cout << "直接使用可靠的备用挖洞方法，避免VTK布尔运算问题..." << std::endl;
        
        // 不再尝试使用布尔运算，完全跳过问题代码部分
        // 直接执行后面的备用方法

        return TryAlternativeHoleCutting(currentGeometry, holeGeometry);
    }
    // 备用挖洞方法 - 基于轮廓的多边形挖洞(精确版本)
    bool TryAlternativeHoleCutting(vtkSmartPointer<vtkPolyData> cubeGeometry, vtkSmartPointer<vtkPolyData> holeGeometry) {
        std::cout << "使用基于轮廓的精确挖洞方法..." << std::endl;
        
        if (!cubeGeometry || cubeGeometry->GetNumberOfCells() == 0) {
            std::cout << "错误: 立方体几何体无效或为空" << std::endl;
            return false;
        }
        
        // 验证多边形轮廓点是否足够
        if (polygonPoints.size() < 9) { // 至少3个点
            std::cout << "错误: 轮廓点数不足" << std::endl;
            return false;
        }
        
        // 创建新的几何体以存储挖孔后的结果
        vtkSmartPointer<vtkPolyData> newGeometry = vtkSmartPointer<vtkPolyData>::New();
        vtkSmartPointer<vtkPoints> newPoints = vtkSmartPointer<vtkPoints>::New();
        vtkSmartPointer<vtkCellArray> newCells = vtkSmartPointer<vtkCellArray>::New();
        
        // 复制所有点
        newPoints->DeepCopy(cubeGeometry->GetPoints());
        
        // 计算多边形轮廓包围盒和中心点
        double holeCenter[3] = {0, 0, 0};
        double minPoint[3] = {DBL_MAX, DBL_MAX, DBL_MAX};
        double maxPoint[3] = {-DBL_MAX, -DBL_MAX, -DBL_MAX};
        
        int pointCount = polygonPoints.size() / 3;
        for (int i = 0; i < pointCount; i++) {
            double point[3] = {
                polygonPoints[i * 3],
                polygonPoints[i * 3 + 1],
                polygonPoints[i * 3 + 2]
            };
            
            // 更新包围盒
            for (int j = 0; j < 3; j++) {
                minPoint[j] = std::min(minPoint[j], point[j]);
                maxPoint[j] = std::max(maxPoint[j], point[j]);
                holeCenter[j] += point[j];
            }
        }
        
        // 计算中心点和包围盒大小
        for (int i = 0; i < 3; i++) {
            holeCenter[i] /= pointCount;
        }
        
        double boundingBoxSize = std::sqrt(
            std::pow(maxPoint[0] - minPoint[0], 2) +
            std::pow(maxPoint[1] - minPoint[1], 2) +
            std::pow(maxPoint[2] - minPoint[2], 2)
        );
        
        std::cout << "轮廓中心点: (" << holeCenter[0] << ", " << holeCenter[1] << ", " << holeCenter[2] << ")" << std::endl;
        std::cout << "轮廓包围盒大小: " << boundingBoxSize << std::endl;
        
        // 创建点到多边形投影距离计算器
        vtkSmartPointer<vtkPolygon> polygon = vtkSmartPointer<vtkPolygon>::New();
        vtkSmartPointer<vtkPoints> polygonPoints = vtkSmartPointer<vtkPoints>::New();
        
        // 添加多边形点
        for (int i = 0; i < pointCount; i++) {
            polygonPoints->InsertNextPoint(
                this->polygonPoints[i * 3],
                this->polygonPoints[i * 3 + 1],
                this->polygonPoints[i * 3 + 2]
            );
        }
        
        polygon->Initialize(pointCount, new vtkIdType[pointCount], polygonPoints);
        
        // 查找多边形法向量
        double normal[3] = {0, 0, 0};
        double center[3] = {0, 0, 0};
        
        // 使用前三个点计算法向量
        if (pointCount >= 3) {
            double p0[3], p1[3], p2[3];
            polygonPoints->GetPoint(0, p0);
            polygonPoints->GetPoint(1, p1);
            polygonPoints->GetPoint(2, p2);
            
            double v1[3] = {p1[0] - p0[0], p1[1] - p0[1], p1[2] - p0[2]};
            double v2[3] = {p2[0] - p0[0], p2[1] - p0[1], p2[2] - p0[2]};
            
            // 叉积计算法向量
            normal[0] = v1[1] * v2[2] - v1[2] * v2[1];
            normal[1] = v1[2] * v2[0] - v1[0] * v2[2];
            normal[2] = v1[0] * v2[1] - v1[1] * v2[0];
            
            // 归一化
            double length = sqrt(normal[0]*normal[0] + normal[1]*normal[1] + normal[2]*normal[2]);
            if (length > 0.0001) {
                normal[0] /= length;
                normal[1] /= length;
                normal[2] /= length;
            } else {
                // 如果无法计算法向量，使用默认值
                normal[0] = 0;
                normal[1] = 0;
                normal[2] = 1;
            }
        }
        
        std::cout << "多边形法向量: (" << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        
        // 计算多边形平面方程: ax + by + cz + d = 0
        double d = -(normal[0] * holeCenter[0] + normal[1] * holeCenter[1] + normal[2] * holeCenter[2]);
        
        // 统计信息
        int removedCells = 0;
        int totalCells = 0;
        vtkIdType numCells = cubeGeometry->GetNumberOfCells();
        
        // 我们需要一个更复杂的算法来检查每个单元是否在轮廓内部
        for (vtkIdType i = 0; i < numCells; i++) {
            vtkCell* cell = cubeGeometry->GetCell(i);
            if (!cell) continue;
            
            totalCells++;
            
            // 计算单元中心
            double cellCenter[3] = {0, 0, 0};
            int numCellPoints = cell->GetNumberOfPoints();
            if (numCellPoints == 0) continue;
            
            for (int j = 0; j < numCellPoints; j++) {
                double point[3];
                cubeGeometry->GetPoint(cell->GetPointId(j), point);
                cellCenter[0] += point[0];
                cellCenter[1] += point[1];
                cellCenter[2] += point[2];
            }
            cellCenter[0] /= numCellPoints;
            cellCenter[1] /= numCellPoints;
            cellCenter[2] /= numCellPoints;
            
            // 检查单元中心到多边形平面的距离
            double distanceToPlane = fabs(normal[0] * cellCenter[0] + normal[1] * cellCenter[1] + 
                                         normal[2] * cellCenter[2] + d);
            
            // 仅考虑接近多边形平面的单元（增加容差范围）
            if (distanceToPlane < boundingBoxSize * 0.25) { // 增加到25%的包围盒大小
                // 将中心点投影到平面上
                double projectedPoint[3];
                double t = -(normal[0] * cellCenter[0] + normal[1] * cellCenter[1] + 
                            normal[2] * cellCenter[2] + d) / 
                            (normal[0] * normal[0] + normal[1] * normal[1] + normal[2] * normal[2]);
                
                projectedPoint[0] = cellCenter[0] + t * normal[0];
                projectedPoint[1] = cellCenter[1] + t * normal[1];
                projectedPoint[2] = cellCenter[2] + t * normal[2];
                
                // 检查投影点是否在多边形内
                int inside = 0;
                double weights[3];
                double pcoords[3];
                
                // 使用更可靠的方法判断点是否在多边形内
                double closestPoint[3];
                double dist2;
                int subId;
                
                bool isInside = false;
                
                // 首先使用VTK的多边形包含点检测
                if (polygon->GetPointIds()->GetNumberOfIds() >= 3) {
                    isInside = (polygon->PointInPolygon(
                        projectedPoint,
                        polygon->GetPointIds()->GetNumberOfIds(),
                        static_cast<double*>(polygonPoints->GetData()->GetVoidPointer(0)),
                        polygon->GetPoints()->GetBounds(),
                        normal) == 1);
                }
                
                // 如果在多边形内，则删除该单元
                if (isInside) {
                    removedCells++;
                    continue; // 不将此单元添加到新几何体
                }
            }
            
            // 如果单元不在挖洞区域内，则保留它
            newCells->InsertNextCell(cell);
        }
        
        std::cout << "处理了总共 " << totalCells << " 个单元" << std::endl;
        std::cout << "移除了 " << removedCells << " 个单元，保留了 " << newCells->GetNumberOfCells() << " 个单元" << std::endl;
        
        // 如果没有移除足够的单元，或者某些特殊情况下，使用备用方法
        if (removedCells == 0 || newCells->GetNumberOfCells() == 0) {
            std::cout << "轮廓挖洞方法未能有效移除单元，尝试增强备用方法..." << std::endl;
            
            // 使用基于距离的多边形挖洞作为备用方法
            // 重置单元数组
            newCells->Reset();
            removedCells = 0;
            
            // 创建二维多边形用于点包含测试
            vtkSmartPointer<vtkPoints> polygonPoints2D = vtkSmartPointer<vtkPoints>::New();
            
            // 投影所有点到一个最佳拟合平面
            vtkSmartPointer<vtkPlane> bestFitPlane = vtkSmartPointer<vtkPlane>::New();
            
            // 计算轮廓法向量最强的坐标轴
            double maxNormalComponent = std::max(std::max(fabs(normal[0]), fabs(normal[1])), fabs(normal[2]));
            int primaryAxis = 0;
            int axis1 = 1;
            int axis2 = 2;
            
            if (fabs(normal[1]) == maxNormalComponent) {
                primaryAxis = 1;
                axis1 = 0;
                axis2 = 2;
            } else if (fabs(normal[2]) == maxNormalComponent) {
                primaryAxis = 2;
                axis1 = 0;
                axis2 = 1;
            }
            
            std::cout << "投影轴: 主轴=" << primaryAxis << ", 平面轴=" << axis1 << "," << axis2 << std::endl;
            
            // 将所有点投影到2D平面
            for (int i = 0; i < pointCount; i++) {
                double point2D[2];
                point2D[0] = this->polygonPoints[i * 3 + axis1];
                point2D[1] = this->polygonPoints[i * 3 + axis2];
                polygonPoints2D->InsertNextPoint(point2D[0], point2D[1], 0);
            }
            
            // 创建多边形测试器
            vtkSmartPointer<vtkPolygon> polygon2D = vtkSmartPointer<vtkPolygon>::New();
            polygon2D->Initialize(pointCount, nullptr, polygonPoints2D);
            
            for (vtkIdType i = 0; i < numCells; i++) {
                vtkCell* cell = cubeGeometry->GetCell(i);
                if (!cell) continue;
                
                // 计算单元中心
                double cellCenter[3] = {0, 0, 0};
                int numCellPoints = cell->GetNumberOfPoints();
                if (numCellPoints == 0) continue;
                
                for (int j = 0; j < numCellPoints; j++) {
                    double point[3];
                    cubeGeometry->GetPoint(cell->GetPointId(j), point);
                    cellCenter[0] += point[0];
                    cellCenter[1] += point[1];
                    cellCenter[2] += point[2];
                }
                cellCenter[0] /= numCellPoints;
                cellCenter[1] /= numCellPoints;
                cellCenter[2] /= numCellPoints;
                
                // 投影单元中心到同一2D平面
                double cell2D[3] = {
                    cellCenter[axis1],
                    cellCenter[axis2],
                    0
                };
                
                // 获取多边形边界
                double bounds[6];
                polygonPoints2D->GetBounds(bounds);
                
                // 检查点是否在多边形内
                double n[3] = {0, 0, 1}; // 2D平面用的标准法向量
                int insidePoly = polygon2D->PointInPolygon(
                    cell2D, 
                    pointCount, 
                    static_cast<double*>(polygonPoints2D->GetData()->GetVoidPointer(0)),
                    bounds,
                    n); // 使用标准法向量而不是原来的法向量，因为我们已经投影到XY平面
                
                // 如果单元中心在多边形内，移除该单元
                if (insidePoly) {
                    removedCells++;
                } else {
                    // 如果不在多边形内，保留该单元
                    newCells->InsertNextCell(cell);
                }
            }
            
            std::cout << "改进后的备用方法移除了 " << removedCells << " 个单元，保留了 " << newCells->GetNumberOfCells() << " 个单元" << std::endl;
        }
        
        // 确保处理结果有效
        if (removedCells > 0 && newCells->GetNumberOfCells() > 0) {
            // 正常情况：已经有单元被移除，继续处理
            std::cout << "挖洞操作成功移除了单元，继续处理..." << std::endl;
            
            newGeometry->SetPoints(newPoints);
            newGeometry->SetPolys(newCells);
            
            try {
                // 更新mapper
                vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
                currentMapper->SetInputData(newGeometry);
                currentMapper->Update();
                
                // 重新渲染
                renderer->GetRenderWindow()->Render();
                
                std::cout << "挖洞方法成功！" << std::endl;
                return true;
            } catch (const std::exception& e) {
                std::cout << "更新几何体时发生异常: " << e.what() << std::endl;
                return false;
            }
        } 
        else {
            // 所有方法都失败，使用强制挖洞方法
            std::cout << "所有挖洞方法都失败，使用强制挖洞..." << std::endl;
                
            // 获取立方体边界和中心
            double bounds[6];
            cubeGeometry->GetBounds(bounds);
            double cubeCenter[3] = {
                (bounds[0] + bounds[1]) / 2.0,
                (bounds[2] + bounds[3]) / 2.0,
                (bounds[4] + bounds[5]) / 2.0
            };
            
            // 强制移除靠近中心的单元
            vtkIdType numCells = cubeGeometry->GetNumberOfCells();
            newCells->Reset(); // 重置单元数组
            int forcedRemovals = 0;
            
            for (vtkIdType i = 0; i < numCells; i++) {
                vtkCell* cell = cubeGeometry->GetCell(i);
                if (!cell) continue;
                
                // 计算单元中心
                double cellCenter[3] = {0, 0, 0};
                int numCellPoints = cell->GetNumberOfPoints();
                if (numCellPoints == 0) continue;
                
                for (int j = 0; j < numCellPoints; j++) {
                    double point[3];
                    cubeGeometry->GetPoint(cell->GetPointId(j), point);
                    cellCenter[0] += point[0];
                    cellCenter[1] += point[1];
                    cellCenter[2] += point[2];
                }
                cellCenter[0] /= numCellPoints;
                cellCenter[1] /= numCellPoints;
                cellCenter[2] /= numCellPoints;
                
                // 计算到中心的距离
                double dx = cellCenter[0] - cubeCenter[0];
                double dy = cellCenter[1] - cubeCenter[1];
                double dz = cellCenter[2] - cubeCenter[2];
                double distance = sqrt(dx*dx + dy*dy + dz*dz);
                
                // 移除接近中心的单元
                if (distance > 0.5) { // 保留远离中心的单元
                    newCells->InsertNextCell(cell);
                } else {
                    forcedRemovals++;
                }
            }
            
            std::cout << "强制挖洞：移除了 " << forcedRemovals << " 个单元" << std::endl;
            
            newGeometry->SetPoints(newPoints);
            newGeometry->SetPolys(newCells);
            
            try {
                // 更新mapper
                vtkPolyDataMapper* currentMapper = vtkPolyDataMapper::SafeDownCast(mainActor->GetMapper());
                currentMapper->SetInputData(newGeometry);
                currentMapper->Update();
                
                // 重新渲染
                renderer->GetRenderWindow()->Render();
                
                std::cout << "备用挖洞方法成功！" << std::endl;
                return true;
            } catch (const std::exception& e) {
                std::cout << "更新几何体时发生异常: " << e.what() << std::endl;
                return false;
            }
        }
    }

    // 屏幕坐标转世界坐标 - 改进版本
    bool ScreenToWorld(int* screenPos, double* worldPos) {
        if (!renderer) return false;

        // 使用更精确的CellPicker，适合细分后的几何体
        vtkSmartPointer<vtkCellPicker> surfacePicker = vtkSmartPointer<vtkCellPicker>::New();
        surfacePicker->SetTolerance(0.001); // 降低容差以提高精度

        std::cout << "尝试在位置 (" << screenPos[0] << ", " << screenPos[1] << ") 进行拾取..." << std::endl;

        if (surfacePicker->Pick(screenPos[0], screenPos[1], 0, renderer)) {
            double* pickedPos = surfacePicker->GetPickPosition();
            worldPos[0] = pickedPos[0];
            worldPos[1] = pickedPos[1];
            worldPos[2] = pickedPos[2];

            vtkIdType cellId = surfacePicker->GetCellId();
            std::cout << "成功拾取到表面，单元ID: " << cellId << std::endl;
            std::cout << "拾取位置: (" << pickedPos[0] << ", " << pickedPos[1] << ", " << pickedPos[2] << ")" << std::endl;
            return true;
        }

        std::cout << "表面拾取失败，尝试备用方法..." << std::endl;

        // 备用方法：使用PointPicker
        vtkSmartPointer<vtkPointPicker> pointPicker = vtkSmartPointer<vtkPointPicker>::New();
        pointPicker->SetTolerance(0.01);

        if (pointPicker->Pick(screenPos[0], screenPos[1], 0, renderer)) {
            double* pickedPos = pointPicker->GetPickPosition();
            worldPos[0] = pickedPos[0];
            worldPos[1] = pickedPos[1];
            worldPos[2] = pickedPos[2];
            std::cout << "点拾取成功: (" << pickedPos[0] << ", " << pickedPos[1] << ", " << pickedPos[2] << ")" << std::endl;
            return true;
        }

        // 最后的备用方法：投影到虚拟平面
        vtkCamera* camera = renderer->GetActiveCamera();
        if (!camera) {
            std::cout << "无法获取相机" << std::endl;
            return false;
        }

        // 获取渲染窗口尺寸
        int* winSize = renderer->GetRenderWindow()->GetSize();

        // 将屏幕坐标转换为标准化设备坐标
        double normalizedX = 2.0 * screenPos[0] / winSize[0] - 1.0;
        double normalizedY = 2.0 * screenPos[1] / winSize[1] - 1.0;

        // 投影到正方体周围的虚拟平面
        double scale = 2.5; // 根据正方体大小调整
        worldPos[0] = normalizedX * scale;
        worldPos[1] = normalizedY * scale;
        worldPos[2] = 1.0; // 稍微偏离正方体表面

        std::cout << "使用投影方法: (" << worldPos[0] << ", " << worldPos[1] << ", " << worldPos[2] << ")" << std::endl;
        return true;
    }
};

vtkStandardNewMacro(CustomInteractorStyle);

// 检查文件是否存在
bool FileExists(const std::string& filename) {
    std::ifstream file(filename);
    return file.good();
}

// 加载PLY模型的函数 - 增强调试版本
vtkSmartPointer<vtkPolyData> LoadPLYModel(const std::string& filename) {
    std::cout << "=== PLY文件加载调试 ===" << std::endl;
    std::cout << "尝试加载PLY文件: " << filename << std::endl;

    // 检查文件是否存在
    if (!FileExists(filename)) {
        std::cout << "错误：PLY文件不存在: " << filename << std::endl;
        return nullptr;
    }

    std::cout << "文件存在，开始读取..." << std::endl;

    // 创建PLY读取器
    vtkSmartPointer<vtkPLYReader> plyReader = vtkSmartPointer<vtkPLYReader>::New();
    plyReader->SetFileName(filename.c_str());

    try {
        std::cout << "执行PLY读取器更新..." << std::endl;
        // 尝试读取文件
        plyReader->Update();

        vtkPolyData* polyData = plyReader->GetOutput();
        if (!polyData) {
            std::cout << "错误：PLY读取器返回空指针" << std::endl;
            return nullptr;
        }

        std::cout << "PLY读取器成功返回数据" << std::endl;
        std::cout << "  原始顶点数: " << polyData->GetNumberOfPoints() << std::endl;
        std::cout << "  原始面片数: " << polyData->GetNumberOfCells() << std::endl;

        if (polyData->GetNumberOfCells() == 0) {
            std::cout << "错误：PLY文件包含0个面片" << std::endl;
            return nullptr;
        }

        if (polyData->GetNumberOfPoints() == 0) {
            std::cout << "错误：PLY文件包含0个顶点" << std::endl;
            return nullptr;
        }

        // 获取模型边界
        double bounds[6];
        polyData->GetBounds(bounds);
        std::cout << "  模型边界: X[" << bounds[0] << ", " << bounds[1] << "], "
                  << "Y[" << bounds[2] << ", " << bounds[3] << "], "
                  << "Z[" << bounds[4] << ", " << bounds[5] << "]" << std::endl;

        // 检查边界是否有效
        bool validBounds = true;
        for (int i = 0; i < 6; i++) {
            if (!std::isfinite(bounds[i])) {
                validBounds = false;
                break;
            }
        }

        if (!validBounds) {
            std::cout << "警告：模型边界包含无效值" << std::endl;
        }

        // 清理数据（移除重复点等）
        std::cout << "开始数据清理..." << std::endl;
        vtkSmartPointer<vtkCleanPolyData> cleanFilter = vtkSmartPointer<vtkCleanPolyData>::New();
        cleanFilter->SetInputData(polyData);
        cleanFilter->Update();

        vtkSmartPointer<vtkPolyData> cleanedData = vtkSmartPointer<vtkPolyData>::New();
        cleanedData->DeepCopy(cleanFilter->GetOutput());

        std::cout << "数据清理完成：" << std::endl;
        std::cout << "  清理后顶点数: " << cleanedData->GetNumberOfPoints() << std::endl;
        std::cout << "  清理后面片数: " << cleanedData->GetNumberOfCells() << std::endl;

        std::cout << "PLY文件加载成功！" << std::endl;
        return cleanedData;

    } catch (const std::exception& e) {
        std::cout << "PLY文件读取异常: " << e.what() << std::endl;
        return nullptr;
    } catch (...) {
        std::cout << "PLY文件读取发生未知异常" << std::endl;
        return nullptr;
    }
}

// 创建默认正方体作为备用 - 增强调试版本
vtkSmartPointer<vtkPolyData> CreateDefaultCube() {
    std::cout << "=== 创建默认正方体调试 ===" << std::endl;
    std::cout << "创建默认正方体作为备用模型..." << std::endl;

    vtkSmartPointer<vtkCubeSource> cubeSource = vtkSmartPointer<vtkCubeSource>::New();
    cubeSource->SetXLength(2.0);
    cubeSource->SetYLength(2.0);
    cubeSource->SetZLength(2.0);
    cubeSource->SetCenter(0.0, 0.0, 0.0);

    std::cout << "正方体源参数设置完成，执行更新..." << std::endl;
    cubeSource->Update();

    vtkPolyData* sourceOutput = cubeSource->GetOutput();
    if (!sourceOutput) {
        std::cout << "错误：正方体源返回空指针" << std::endl;
        return nullptr;
    }

    std::cout << "正方体源输出验证：" << std::endl;
    std::cout << "  顶点数: " << sourceOutput->GetNumberOfPoints() << std::endl;
    std::cout << "  面片数: " << sourceOutput->GetNumberOfCells() << std::endl;

    // 获取边界
    double bounds[6];
    sourceOutput->GetBounds(bounds);
    std::cout << "  边界: X[" << bounds[0] << ", " << bounds[1] << "], "
              << "Y[" << bounds[2] << ", " << bounds[3] << "], "
              << "Z[" << bounds[4] << ", " << bounds[5] << "]" << std::endl;

    vtkSmartPointer<vtkPolyData> cubeData = vtkSmartPointer<vtkPolyData>::New();
    cubeData->DeepCopy(sourceOutput);

    std::cout << "默认正方体创建完成并验证成功" << std::endl;
    return cubeData;
}

// 主函数
int main() {
    std::cout << "启动交互式3D模型挖洞应用程序..." << std::endl;
    std::cout << "操作说明:" << std::endl;
    std::cout << "  N键 - 切换到普通视图模式（旋转、缩放、平移）" << std::endl;
    std::cout << "  E键 - 切换到擦除模式（点击擦除区域）" << std::endl;
    std::cout << "  D键 - 切换到挖洞模式（左键选择轮廓点，右键完成挖洞）" << std::endl;
    std::cout << "  ESC键 - 退出程序" << std::endl;
    std::cout << "挖洞功能：在D模式下选择3个或更多点形成封闭轮廓，右键执行挖洞操作" << std::endl;
    std::cout << std::endl;

    // 尝试加载PLY模型 - 检查多个可能的路径
    std::vector<std::string> possiblePaths = {
        "mesh20240409.ply",           // 当前目录
        "../mesh20240409.ply",        // 上级目录
        "../../mesh20240409.ply",     // 上上级目录
        "/home/<USER>/CLionProjects/myapp_vtk_ubuntu/mesh20240409.ply"  // 绝对路径
    };

    std::string plyFilename;
    vtkSmartPointer<vtkPolyData> modelData = nullptr;

    // 尝试每个可能的路径
    for (const auto& path : possiblePaths) {
        std::cout << "尝试路径: " << path << std::endl;
        if (FileExists(path)) {
            plyFilename = path;
            modelData = LoadPLYModel(plyFilename);
            if (modelData) {
                std::cout << "成功从路径加载PLY文件: " << path << std::endl;
                break;
            }
        }
    }
    // 如果所有路径都失败，使用默认正方体
    if (!modelData) {
        std::cout << "所有PLY文件路径都失败，使用默认正方体模型" << std::endl;
        modelData = CreateDefaultCube();
    }

    if (!modelData) {
        std::cout << "错误：无法创建任何几何体，程序退出" << std::endl;
        return -1;
    }

    std::cout << "=== 几何体处理调试 ===" << std::endl;
    std::cout << "输入模型数据验证：" << std::endl;
    std::cout << "  顶点数: " << modelData->GetNumberOfPoints() << std::endl;
    std::cout << "  面片数: " << modelData->GetNumberOfCells() << std::endl;

    // 对模型进行三角化处理（确保所有面都是三角形）
    std::cout << "开始三角化处理..." << std::endl;
    vtkSmartPointer<vtkTriangleFilter> triangleFilter = vtkSmartPointer<vtkTriangleFilter>::New();
    triangleFilter->SetInputData(modelData);
    triangleFilter->Update();

    vtkPolyData* triangulatedData = triangleFilter->GetOutput();
    std::cout << "三角化完成：" << std::endl;
    std::cout << "  顶点数: " << triangulatedData->GetNumberOfPoints() << std::endl;
    std::cout << "  面片数: " << triangulatedData->GetNumberOfCells() << std::endl;

    // 验证三角化结果
    if (!triangulatedData || triangulatedData->GetNumberOfCells() == 0) {
        std::cout << "错误：三角化失败，程序退出" << std::endl;
        return -1;
    }

    // 跳过细分滤波器，直接使用三角化后的几何体
    // PLY模型已经有足够的面片密度（159396个面片），无需进一步细分
    std::cout << "跳过细分处理，PLY模型已有足够的面片密度用于精确拾取" << std::endl;

    vtkPolyData* finalGeometry = triangulatedData;
    std::cout << "最终几何体验证：" << std::endl;
    std::cout << "  顶点数: " << finalGeometry->GetNumberOfPoints() << std::endl;
    std::cout << "  面片数: " << finalGeometry->GetNumberOfCells() << std::endl;

    // 验证最终几何体
    if (!finalGeometry || finalGeometry->GetNumberOfCells() == 0) {
        std::cout << "错误：最终几何体无效，程序退出" << std::endl;
        return -1;
    }

    // 获取最终边界
    double finalBounds[6];
    finalGeometry->GetBounds(finalBounds);
    std::cout << "  最终边界: X[" << finalBounds[0] << ", " << finalBounds[1] << "], "
              << "Y[" << finalBounds[2] << ", " << finalBounds[3] << "], "
              << "Z[" << finalBounds[4] << ", " << finalBounds[5] << "]" << std::endl;

    std::cout << "=== 渲染设置调试 ===" << std::endl;

    // 创建mapper
    std::cout << "创建mapper..." << std::endl;
    vtkSmartPointer<vtkPolyDataMapper> mapper = vtkSmartPointer<vtkPolyDataMapper>::New();
    mapper->SetInputConnection(triangleFilter->GetOutputPort());
    mapper->Update();

    std::cout << "Mapper创建完成，输入数据验证：" << std::endl;
    vtkPolyData* mapperInput = mapper->GetInput();
    if (mapperInput) {
        std::cout << "  Mapper输入顶点数: " << mapperInput->GetNumberOfPoints() << std::endl;
        std::cout << "  Mapper输入面片数: " << mapperInput->GetNumberOfCells() << std::endl;
    } else {
        std::cout << "  警告：Mapper输入为空" << std::endl;
    }

    // 创建actor
    std::cout << "创建actor..." << std::endl;
    vtkSmartPointer<vtkActor> actor = vtkSmartPointer<vtkActor>::New();
    actor->SetMapper(mapper);

    // 设置材质属性 - 增强可见性
    std::cout << "设置材质属性..." << std::endl;
    actor->GetProperty()->SetColor(0.8, 0.8, 0.2); // 明亮的黄色，更容易看见
    actor->GetProperty()->SetSpecular(0.2);  // 降低镜面反射
    actor->GetProperty()->SetSpecularPower(10);
    actor->GetProperty()->SetAmbient(0.6);   // 大幅增加环境光
    actor->GetProperty()->SetDiffuse(0.9);   // 增加漫反射
    actor->GetProperty()->SetOpacity(1.0);   // 确保完全不透明

    // 禁用背面剔除，确保所有面都可见
    actor->GetProperty()->SetBackfaceCulling(false);
    actor->GetProperty()->SetFrontfaceCulling(false);

    // 强制设置为实体渲染
    actor->GetProperty()->SetRepresentationToSurface();

    std::cout << "材质属性设置完成" << std::endl;

    // 创建渲染器
    std::cout << "创建渲染器..." << std::endl;
    vtkSmartPointer<vtkRenderer> renderer = vtkSmartPointer<vtkRenderer>::New();
    renderer->AddActor(actor);
    renderer->SetBackground(0.2, 0.2, 0.3); // 稍微亮一点的背景，便于观察

    std::cout << "Actor已添加到渲染器" << std::endl;

    // 设置相机 - 根据模型边界自动调整
    std::cout << "=== 相机设置调试 ===" << std::endl;
    vtkCamera* camera = renderer->GetActiveCamera();

    // 获取模型边界
    double bounds[6];
    finalGeometry->GetBounds(bounds);

    std::cout << "模型边界详细信息：" << std::endl;
    std::cout << "  X: [" << bounds[0] << ", " << bounds[1] << "] 范围: " << (bounds[1] - bounds[0]) << std::endl;
    std::cout << "  Y: [" << bounds[2] << ", " << bounds[3] << "] 范围: " << (bounds[3] - bounds[2]) << std::endl;
    std::cout << "  Z: [" << bounds[4] << ", " << bounds[5] << "] 范围: " << (bounds[5] - bounds[4]) << std::endl;

    // 计算模型中心和尺寸
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };

    double maxDimension = std::max({
        bounds[1] - bounds[0],
        bounds[3] - bounds[2],
        bounds[5] - bounds[4]
    });

    std::cout << "模型中心: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
    std::cout << "最大尺寸: " << maxDimension << std::endl;

    // 设置相机位置，距离模型适当距离
    double distance = maxDimension * 3.0; // 增加距离确保模型完全可见
    double cameraPos[3] = {
        center[0] + distance * 0.7,
        center[1] + distance * 0.7,
        center[2] + distance * 0.7
    };

    camera->SetPosition(cameraPos[0], cameraPos[1], cameraPos[2]);
    camera->SetFocalPoint(center[0], center[1], center[2]);
    camera->SetViewUp(0, 1, 0);

    // 设置合适的视角
    camera->SetViewAngle(45.0);

    std::cout << "相机设置完成：" << std::endl;
    std::cout << "  位置: (" << cameraPos[0] << ", " << cameraPos[1] << ", " << cameraPos[2] << ")" << std::endl;
    std::cout << "  焦点: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
    std::cout << "  距离: " << distance << std::endl;

    // 添加多个光源以确保所有面都被照亮 - 根据模型调整
    // 主光源
    vtkSmartPointer<vtkLight> mainLight = vtkSmartPointer<vtkLight>::New();
    mainLight->SetPosition(center[0] + distance, center[1] + distance, center[2] + distance);
    mainLight->SetFocalPoint(center[0], center[1], center[2]);
    mainLight->SetColor(1.0, 1.0, 1.0);
    mainLight->SetIntensity(0.8);
    renderer->AddLight(mainLight);

    // 补充光源1 - 从另一个角度照亮
    vtkSmartPointer<vtkLight> fillLight1 = vtkSmartPointer<vtkLight>::New();
    fillLight1->SetPosition(center[0] - distance, center[1] - distance, center[2] + distance);
    fillLight1->SetFocalPoint(center[0], center[1], center[2]);
    fillLight1->SetColor(1.0, 1.0, 1.0);
    fillLight1->SetIntensity(0.4);
    renderer->AddLight(fillLight1);

    // 补充光源2 - 从背后照亮
    vtkSmartPointer<vtkLight> fillLight2 = vtkSmartPointer<vtkLight>::New();
    fillLight2->SetPosition(center[0], center[1], center[2] - distance);
    fillLight2->SetFocalPoint(center[0], center[1], center[2]);
    fillLight2->SetColor(1.0, 1.0, 1.0);
    fillLight2->SetIntensity(0.3);
    renderer->AddLight(fillLight2);

    // 创建状态文本
    vtkSmartPointer<vtkTextActor> statusText = vtkSmartPointer<vtkTextActor>::New();
    statusText->SetPosition(10, 10);
    statusText->GetTextProperty()->SetFontSize(14);
    statusText->GetTextProperty()->SetColor(1.0, 1.0, 1.0);
    statusText->SetInput("模式: 普通视图 | E-擦除, D-绘制, 鼠标-旋转/缩放/平移, ESC-退出");
    renderer->AddActor2D(statusText);

    // 创建渲染窗口
    std::cout << "=== 渲染窗口创建调试 ===" << std::endl;
    std::cout << "创建渲染窗口..." << std::endl;
    vtkSmartPointer<vtkRenderWindow> renderWindow = vtkSmartPointer<vtkRenderWindow>::New();
    renderWindow->AddRenderer(renderer);
    renderWindow->SetSize(1024, 768);
    renderWindow->SetWindowName("交互式3D模型挖洞 - VTK应用程序");

    std::cout << "渲染窗口设置完成，尺寸: 1024x768" << std::endl;

    // 创建交互器
    vtkSmartPointer<vtkRenderWindowInteractor> renderWindowInteractor =
        vtkSmartPointer<vtkRenderWindowInteractor>::New();
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 创建自定义交互样式
    vtkSmartPointer<CustomInteractorStyle> style = vtkSmartPointer<CustomInteractorStyle>::New();
    style->SetMainActor(actor);
    style->SetRenderer(renderer);
    style->SetStatusText(statusText);
    renderWindowInteractor->SetInteractorStyle(style);

    // 开始渲染和交互
    std::cout << "=== 启动渲染 ===" << std::endl;
    std::cout << "执行初始渲染..." << std::endl;

    try {
        renderWindow->Render();
        std::cout << "初始渲染完成，启动交互器..." << std::endl;

        // 重置相机以确保模型完全可见
        renderer->ResetCamera();
        std::cout << "相机已重置" << std::endl;

        // 再次渲染
        renderWindow->Render();
        std::cout << "第二次渲染完成" << std::endl;

        std::cout << "启动交互循环..." << std::endl;
        renderWindowInteractor->Start();

    } catch (const std::exception& e) {
        std::cout << "渲染异常: " << e.what() << std::endl;
        return -1;
    } catch (...) {
        std::cout << "渲染发生未知异常" << std::endl;
        return -1;
    }

    std::cout << "应用程序已退出。" << std::endl;
    return 0;
}