#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkCellPicker.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkNamedColors.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>
#include <vtkSphereSource.h>
#include <vtkUnsignedCharArray.h>
#include <vtkCellData.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
#include <vtkSmartPointer.h>

#include <iostream>
#include <vector>

// 简化的交互器样式类
class SimpleMouseInteractorStyle : public vtkInteractorStyleTrackballCamera
{
public:
    static SimpleMouseInteractorStyle* New();
    vtkTypeMacro(SimpleMouseInteractorStyle, vtkInteractorStyleTrackballCamera);

    virtual void OnLeftButtonDown() override
    {
        // 获取鼠标点击位置
        int* clickPos = this->GetInteractor()->GetEventPosition();
        
        // 使用拾取器获取点击的表面点
        this->Picker->SetTolerance(0.0005);
        this->Picker->Pick(clickPos[0], clickPos[1], 0, this->CurrentRenderer);
        
        double* worldPosition = this->Picker->GetPickPosition();
        vtkIdType cellId = this->Picker->GetCellId();
        
        if (cellId != -1)
        {
            std::cout << "选中点: (" << worldPosition[0] << ", " << worldPosition[1] 
                      << ", " << worldPosition[2] << "), Cell ID: " << cellId << std::endl;
            
            // 将选中的面着色为红色
            if (this->Colors && cellId < this->Colors->GetNumberOfTuples())
            {
                this->Colors->SetTuple3(cellId, 255, 0, 0); // 红色
                this->Colors->Modified();
                this->PolyData->Modified();
                
                // 刷新渲染
                this->GetInteractor()->GetRenderWindow()->Render();
            }
        }
        
        // 调用父类方法以保持正常的相机交互
        vtkInteractorStyleTrackballCamera::OnLeftButtonDown();
    }

    virtual void OnKeyPress() override
    {
        std::string key = this->GetInteractor()->GetKeySym();
        
        if (key == "r" || key == "R")
        {
            // 重置颜色
            this->ResetColors();
            std::cout << "已重置颜色" << std::endl;
        }
        
        vtkInteractorStyleTrackballCamera::OnKeyPress();
    }

    void SetPicker(vtkCellPicker* picker) { this->Picker = picker; }
    void SetPolyData(vtkPolyData* polyData) 
    { 
        this->PolyData = polyData;
        this->InitializeColors();
    }
    void SetRenderer(vtkRenderer* renderer) { this->CurrentRenderer = renderer; }

protected:
    SimpleMouseInteractorStyle()
    {
        this->Picker = nullptr;
        this->PolyData = nullptr;
        this->CurrentRenderer = nullptr;
    }

    void InitializeColors()
    {
        if (!this->PolyData) return;
        
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        this->Colors = vtkSmartPointer<vtkUnsignedCharArray>::New();
        this->Colors->SetNumberOfComponents(3);
        this->Colors->SetName("Colors");
        this->Colors->SetNumberOfTuples(numCells);
        
        // 设置默认颜色为浅灰色
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->PolyData->GetCellData()->SetScalars(this->Colors);
    }

    void ResetColors()
    {
        if (!this->Colors) return;
        
        vtkIdType numCells = this->Colors->GetNumberOfTuples();
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->Colors->Modified();
        this->PolyData->Modified();
        this->GetInteractor()->GetRenderWindow()->Render();
    }

private:
    vtkCellPicker* Picker;
    vtkPolyData* PolyData;
    vtkRenderer* CurrentRenderer;
    vtkSmartPointer<vtkUnsignedCharArray> Colors;
};

vtkStandardNewMacro(SimpleMouseInteractorStyle);

int main()
{
    std::cout << "=== VTK 简化版交互式着色应用程序 ===" << std::endl;
    std::cout << "鼠标左键点击表面进行着色，按 'R' 重置" << std::endl;

    // 创建颜色对象
    vtkNew<vtkNamedColors> colors;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "成功加载PLY文件" << std::endl;
    
    vtkPolyData* polyData = reader->GetOutput();
    std::cout << "顶点数: " << polyData->GetNumberOfPoints() 
              << ", 面数: " << polyData->GetNumberOfCells() << std::endl;

    // 创建映射器和演员
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(colors->GetColor3d("DarkSlateGray").GetData());

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK 简化版交互式着色");
    renderWindow->SetSize(1200, 800);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 创建拾取器和交互器样式
    vtkNew<vtkCellPicker> picker;
    vtkNew<SimpleMouseInteractorStyle> style;
    style->SetPicker(picker);
    style->SetPolyData(polyData);
    style->SetRenderer(renderer);

    renderWindowInteractor->SetInteractorStyle(style);

    // 设置相机
    vtkCamera* camera = renderer->GetActiveCamera();
    double bounds[6];
    polyData->GetBounds(bounds);
    
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };
    
    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));
    
    camera->SetPosition(center[0] + size, center[1] + size, center[2] + size);
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 0, 1);

    // 开始渲染和交互
    renderWindow->Render();
    std::cout << "程序已启动，请点击模型表面进行着色..." << std::endl;
    renderWindowInteractor->Start();

    return EXIT_SUCCESS;
}
