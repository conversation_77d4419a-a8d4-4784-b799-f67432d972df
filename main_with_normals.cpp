#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkCellPicker.h>
#include <vtkInteractorStyleTrackballCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>
#include <vtkSphereSource.h>
#include <vtkUnsignedCharArray.h>
#include <vtkCellData.h>
#include <vtkTextActor.h>
#include <vtkTextProperty.h>
#include <vtkSmartPointer.h>
#include <vtkMath.h>
#include <vtkCell.h>
#include <vtkPolyDataNormals.h>
#include <vtkFloatArray.h>
#include <vtkPointData.h>

#include <iostream>
#include <vector>
#include <array>
#include <cmath>

// 带法向量计算的交互器样式类
class NormalBasedInteractorStyle : public vtkInteractorStyleTrackballCamera
{
public:
    static NormalBasedInteractorStyle* New();
    vtkTypeMacro(NormalBasedInteractorStyle, vtkInteractorStyleTrackballCamera);

    virtual void OnLeftButtonDown() override
    {
        int* clickPos = this->GetInteractor()->GetEventPosition();
        
        this->Picker->SetTolerance(0.001);
        this->Picker->Pick(clickPos[0], clickPos[1], 0, this->CurrentRenderer);
        
        double* worldPosition = this->Picker->GetPickPosition();
        vtkIdType cellId = this->Picker->GetCellId();
        
        if (cellId != -1)
        {
            std::cout << "选中点 " << this->SelectedPoints.size() + 1
                      << ": (" << worldPosition[0] << ", " << worldPosition[1]
                      << ", " << worldPosition[2] << ")" << std::endl;
            
            // 显示面法向量
            this->DisplayCellNormal(cellId);
            
            this->SelectedPoints.push_back({worldPosition[0], worldPosition[1], worldPosition[2]});
            this->SelectedCellIds.push_back(cellId);
            
            this->AddMarkerSphere(worldPosition);
            this->UpdateStatusText();
            
            if (this->SelectedPoints.size() >= 3)
            {
                this->ColorRegionWithNormals();
            }
            
            this->GetInteractor()->GetRenderWindow()->Render();
        }
        
        vtkInteractorStyleTrackballCamera::OnLeftButtonDown();
    }

    virtual void OnKeyPress() override
    {
        std::string key = this->GetInteractor()->GetKeySym();
        
        if (key == "r" || key == "R")
        {
            this->ResetSelection();
            std::cout << "已重置选择" << std::endl;
        }
        else if (key == "c" || key == "C")
        {
            this->ClearColoring();
            std::cout << "已清除着色" << std::endl;
        }
        else if (key == "d" || key == "D")
        {
            this->DebugNormals();
        }
        
        vtkInteractorStyleTrackballCamera::OnKeyPress();
    }

    void SetPicker(vtkCellPicker* picker) { this->Picker = picker; }
    void SetPolyData(vtkPolyData* polyData) 
    { 
        this->PolyData = polyData;
        this->InitializeColors();
    }
    void SetRenderer(vtkRenderer* renderer) { this->CurrentRenderer = renderer; }
    void SetTextActor(vtkTextActor* textActor) { this->StatusTextActor = textActor; }

protected:
    NormalBasedInteractorStyle()
    {
        this->Picker = nullptr;
        this->PolyData = nullptr;
        this->CurrentRenderer = nullptr;
        this->StatusTextActor = nullptr;
    }

    void InitializeColors()
    {
        if (!this->PolyData) return;
        
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        this->Colors = vtkSmartPointer<vtkUnsignedCharArray>::New();
        this->Colors->SetNumberOfComponents(3);
        this->Colors->SetName("Colors");
        this->Colors->SetNumberOfTuples(numCells);
        
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->PolyData->GetCellData()->SetScalars(this->Colors);
    }

    void AddMarkerSphere(double* position)
    {
        vtkNew<vtkSphereSource> sphereSource;
        sphereSource->SetCenter(position[0], position[1], position[2]);
        sphereSource->SetRadius(5.0);
        sphereSource->Update();
        
        vtkNew<vtkPolyDataMapper> sphereMapper;
        sphereMapper->SetInputConnection(sphereSource->GetOutputPort());
        
        vtkNew<vtkActor> sphereActor;
        sphereActor->SetMapper(sphereMapper);
        sphereActor->GetProperty()->SetColor(1.0, 1.0, 0.0);
        
        this->MarkerActors.push_back(sphereActor);
        this->CurrentRenderer->AddActor(sphereActor);
    }

    void DisplayCellNormal(vtkIdType cellId)
    {
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        if (cellNormals)
        {
            double normal[3];
            cellNormals->GetTuple(cellId, normal);
            std::cout << "  面法向量: (" << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        }
        else
        {
            std::cout << "  警告: 该面没有法向量信息" << std::endl;
        }
    }

    void ColorRegionWithNormals()
    {
        if (this->SelectedPoints.size() < 3 || !this->PolyData || !this->Colors) return;
        
        std::cout << "开始基于法向量的精确区域着色..." << std::endl;
        
        // 使用预计算的法向量计算平均法向量
        double avgNormal[3] = {0.0, 0.0, 0.0};
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        
        if (cellNormals && this->SelectedCellIds.size() >= 3)
        {
            for (vtkIdType cellId : this->SelectedCellIds)
            {
                double cellNormal[3];
                cellNormals->GetTuple(cellId, cellNormal);
                
                avgNormal[0] += cellNormal[0];
                avgNormal[1] += cellNormal[1];
                avgNormal[2] += cellNormal[2];
            }
            
            double magnitude = sqrt(avgNormal[0]*avgNormal[0] + avgNormal[1]*avgNormal[1] + avgNormal[2]*avgNormal[2]);
            if (magnitude > 1e-10)
            {
                avgNormal[0] /= magnitude;
                avgNormal[1] /= magnitude;
                avgNormal[2] /= magnitude;
            }
        }
        
        std::cout << "平均法向量: (" << avgNormal[0] << ", " << avgNormal[1] << ", " << avgNormal[2] << ")" << std::endl;
        
        // 简化的区域着色：基于距离和法向量相似性
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        int coloredCells = 0;
        
        // 计算选中点的中心
        double center[3] = {0.0, 0.0, 0.0};
        for (const auto& point : this->SelectedPoints)
        {
            center[0] += point[0];
            center[1] += point[1];
            center[2] += point[2];
        }
        center[0] /= this->SelectedPoints.size();
        center[1] /= this->SelectedPoints.size();
        center[2] /= this->SelectedPoints.size();
        
        // 计算选中区域的大致半径
        double maxDist = 0.0;
        for (const auto& point : this->SelectedPoints)
        {
            double dist = sqrt((point[0] - center[0]) * (point[0] - center[0]) +
                              (point[1] - center[1]) * (point[1] - center[1]) +
                              (point[2] - center[2]) * (point[2] - center[2]));
            if (dist > maxDist) maxDist = dist;
        }
        
        std::cout << "区域中心: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
        std::cout << "区域半径: " << maxDist << std::endl;
        
        for (vtkIdType cellId = 0; cellId < numCells; cellId++)
        {
            // 获取面中心
            double cellCenter[3];
            this->GetCellCenter(cellId, cellCenter);
            
            // 计算到选中区域中心的距离
            double dist = sqrt((cellCenter[0] - center[0]) * (cellCenter[0] - center[0]) +
                              (cellCenter[1] - center[1]) * (cellCenter[1] - center[1]) +
                              (cellCenter[2] - center[2]) * (cellCenter[2] - center[2]));
            
            // 如果在区域内，检查法向量相似性
            if (dist <= maxDist * 1.2) // 稍微扩大范围
            {
                if (cellNormals)
                {
                    double cellNormal[3];
                    cellNormals->GetTuple(cellId, cellNormal);
                    
                    // 计算法向量相似性
                    double similarity = vtkMath::Dot(avgNormal, cellNormal);
                    
                    if (similarity > 0.7) // 法向量相似度阈值
                    {
                        this->Colors->SetTuple3(cellId, 255, 0, 0);
                        coloredCells++;
                    }
                }
                else
                {
                    // 如果没有法向量，仅基于距离
                    this->Colors->SetTuple3(cellId, 255, 0, 0);
                    coloredCells++;
                }
            }
        }
        
        std::cout << "基于法向量着色完成，共着色 " << coloredCells << " 个面" << std::endl;
        
        this->Colors->Modified();
        this->PolyData->Modified();
    }

private:
    vtkCellPicker* Picker;
    vtkPolyData* PolyData;
    vtkRenderer* CurrentRenderer;
    vtkTextActor* StatusTextActor;
    vtkSmartPointer<vtkUnsignedCharArray> Colors;
    
    std::vector<std::array<double, 3>> SelectedPoints;
    std::vector<vtkIdType> SelectedCellIds;
    std::vector<vtkSmartPointer<vtkActor>> MarkerActors;
    
    void UpdateStatusText()
    {
        if (!this->StatusTextActor) return;
        
        std::string statusText = "已选择点数: " + std::to_string(this->SelectedPoints.size()) + 
                                "\n按 'R' 重置，按 'C' 清除，按 'D' 调试法向量";
        this->StatusTextActor->SetInput(statusText.c_str());
    }
    
    void ResetSelection()
    {
        this->SelectedPoints.clear();
        this->SelectedCellIds.clear();
        
        for (auto& actor : this->MarkerActors)
        {
            this->CurrentRenderer->RemoveActor(actor);
        }
        this->MarkerActors.clear();
        
        this->ClearColoring();
        this->UpdateStatusText();
        this->GetInteractor()->GetRenderWindow()->Render();
    }
    
    void ClearColoring()
    {
        if (!this->PolyData || !this->Colors) return;
        
        vtkIdType numCells = this->PolyData->GetNumberOfCells();
        for (vtkIdType i = 0; i < numCells; i++)
        {
            this->Colors->SetTuple3(i, 200, 200, 200);
        }
        
        this->Colors->Modified();
        this->PolyData->Modified();
        this->GetInteractor()->GetRenderWindow()->Render();
    }
    
    void GetCellCenter(vtkIdType cellId, double center[3])
    {
        vtkCell* cell = this->PolyData->GetCell(cellId);
        vtkPoints* points = cell->GetPoints();
        int numPoints = points->GetNumberOfPoints();
        
        center[0] = center[1] = center[2] = 0.0;
        
        for (int i = 0; i < numPoints; i++)
        {
            double point[3];
            points->GetPoint(i, point);
            center[0] += point[0];
            center[1] += point[1];
            center[2] += point[2];
        }
        
        center[0] /= numPoints;
        center[1] /= numPoints;
        center[2] /= numPoints;
    }
    
    void DebugNormals()
    {
        if (this->SelectedCellIds.empty()) 
        {
            std::cout << "没有选中的面" << std::endl;
            return;
        }
        
        std::cout << "\n=== 法向量调试信息 ===" << std::endl;
        
        vtkDataArray* cellNormals = this->PolyData->GetCellData()->GetNormals();
        if (cellNormals)
        {
            for (size_t i = 0; i < this->SelectedCellIds.size(); i++)
            {
                double normal[3];
                cellNormals->GetTuple(this->SelectedCellIds[i], normal);
                std::cout << "面 " << i+1 << " (ID:" << this->SelectedCellIds[i] << "): (" 
                          << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
            }
        }
        else
        {
            std::cout << "模型没有法向量信息" << std::endl;
        }
        
        std::cout << "===================" << std::endl;
    }
};

vtkStandardNewMacro(NormalBasedInteractorStyle);

int main()
{
    std::cout << "=== VTK 基于法向量的精确区域着色 ===" << std::endl;
    std::cout << "改进功能：" << std::endl;
    std::cout << "1. 预计算模型法向量" << std::endl;
    std::cout << "2. 基于法向量相似性的区域判断" << std::endl;
    std::cout << "3. 距离和法向量双重过滤" << std::endl;
    std::cout << "4. 实时法向量信息显示" << std::endl;
    std::cout << "操作：左键选点，R重置，C清除，D调试" << std::endl;
    std::cout << "=====================================" << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "成功加载PLY文件" << std::endl;

    vtkPolyData* polyData = reader->GetOutput();
    std::cout << "原始模型 - 顶点数: " << polyData->GetNumberOfPoints()
              << ", 面数: " << polyData->GetNumberOfCells() << std::endl;

    // 计算模型的法向量
    std::cout << "正在计算模型法向量..." << std::endl;
    vtkNew<vtkPolyDataNormals> normalGenerator;
    normalGenerator->SetInputData(polyData);
    normalGenerator->ComputePointNormalsOn();
    normalGenerator->ComputeCellNormalsOn();
    normalGenerator->SplittingOff();
    normalGenerator->ConsistencyOn();
    normalGenerator->AutoOrientNormalsOn();
    normalGenerator->Update();

    polyData = normalGenerator->GetOutput();
    std::cout << "法向量计算完成" << std::endl;

    // 验证法向量
    vtkDataArray* pointNormals = polyData->GetPointData()->GetNormals();
    vtkDataArray* cellNormals = polyData->GetCellData()->GetNormals();

    if (pointNormals)
        std::cout << "点法向量数量: " << pointNormals->GetNumberOfTuples() << std::endl;
    if (cellNormals)
        std::cout << "面法向量数量: " << cellNormals->GetNumberOfTuples() << std::endl;

    // 创建映射器和演员
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputData(polyData);

    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.9, 0.9, 0.9);

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.1, 0.2, 0.4);

    // 创建状态文本
    vtkNew<vtkTextActor> statusTextActor;
    statusTextActor->SetPosition(10, 10);
    statusTextActor->GetTextProperty()->SetFontSize(16);
    statusTextActor->GetTextProperty()->SetColor(1.0, 1.0, 1.0);
    statusTextActor->SetInput("已选择点数: 0\n按 'R' 重置，按 'C' 清除，按 'D' 调试法向量");
    renderer->AddActor2D(statusTextActor);

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK 基于法向量的精确区域着色");
    renderWindow->SetSize(1200, 800);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 创建拾取器和交互器样式
    vtkNew<vtkCellPicker> picker;
    vtkNew<NormalBasedInteractorStyle> style;
    style->SetPicker(picker);
    style->SetPolyData(polyData);
    style->SetRenderer(renderer);
    style->SetTextActor(statusTextActor);

    renderWindowInteractor->SetInteractorStyle(style);

    // 设置相机
    vtkCamera* camera = renderer->GetActiveCamera();
    double bounds[6];
    polyData->GetBounds(bounds);

    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };

    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));

    camera->SetPosition(center[0], center[1], center[2] + size * 2.0);
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0);

    renderer->ResetCamera();
    camera->Zoom(0.9);

    std::cout << "模型边界: X[" << bounds[0] << ", " << bounds[1] << "] "
              << "Y[" << bounds[2] << ", " << bounds[3] << "] "
              << "Z[" << bounds[4] << ", " << bounds[5] << "]" << std::endl;

    // 开始渲染和交互
    renderWindow->Render();
    std::cout << "\n程序已启动，开始基于法向量的精确区域着色..." << std::endl;
    renderWindowInteractor->Start();

    return EXIT_SUCCESS;
}
