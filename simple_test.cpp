#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>

#include <iostream>

int main()
{
    std::cout << "Simple VTK Test..." << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "PLY file loaded" << std::endl;
    
    vtkPolyData* polyData = reader->GetOutput();
    std::cout << "Points: " << polyData->GetNumberOfPoints() 
              << ", Cells: " << polyData->GetNumberOfCells() << std::endl;

    // 创建映射器和演员
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputConnection(reader->GetOutputPort());

    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.8, 0.8, 0.8);

    // 创建渲染器
    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.1, 0.2, 0.4);

    // 创建渲染窗口
    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("Simple VTK Test");
    renderWindow->SetSize(800, 600);

    // 创建交互器
    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 设置相机
    vtkCamera* camera = renderer->GetActiveCamera();
    double bounds[6];
    polyData->GetBounds(bounds);
    
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };
    
    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));
    
    camera->SetPosition(center[0], center[1], center[2] + size * 2.0);
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0);
    
    renderer->ResetCamera();
    camera->Zoom(0.9);

    std::cout << "Starting render..." << std::endl;
    renderWindow->Render();
    
    std::cout << "Test completed!" << std::endl;
    
    renderWindowInteractor->Start();

    return 0;
}
