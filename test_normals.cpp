#include <vtkActor.h>
#include <vtkCamera.h>
#include <vtkNew.h>
#include <vtkPLYReader.h>
#include <vtkPolyData.h>
#include <vtkPolyDataMapper.h>
#include <vtkProperty.h>
#include <vtkRenderWindow.h>
#include <vtkRenderWindowInteractor.h>
#include <vtkRenderer.h>
#include <vtkPolyDataNormals.h>
#include <vtkPointData.h>
#include <vtkCellData.h>

#include <iostream>

int main()
{
    std::cout << "Testing VTK Normal Calculation..." << std::endl;

    // 读取PLY文件
    vtkNew<vtkPLYReader> reader;
    reader->SetFileName("mesh20240409.ply");
    reader->Update();

    std::cout << "PLY file loaded successfully" << std::endl;
    
    vtkPolyData* originalData = reader->GetOutput();
    std::cout << "Original model - Points: " << originalData->GetNumberOfPoints() 
              << ", Cells: " << originalData->GetNumberOfCells() << std::endl;

    // 计算法向量
    std::cout << "Computing normals..." << std::endl;
    vtkNew<vtkPolyDataNormals> normalGenerator;
    normalGenerator->SetInputData(originalData);
    normalGenerator->ComputePointNormalsOn();
    normalGenerator->ComputeCellNormalsOn();
    normalGenerator->SplittingOff();
    normalGenerator->ConsistencyOn();
    normalGenerator->AutoOrientNormalsOn();
    normalGenerator->Update();
    
    vtkPolyData* polyDataWithNormals = normalGenerator->GetOutput();
    std::cout << "Normals computed successfully" << std::endl;
    
    // 验证法向量
    vtkDataArray* pointNormals = polyDataWithNormals->GetPointData()->GetNormals();
    vtkDataArray* cellNormals = polyDataWithNormals->GetCellData()->GetNormals();
    
    if (pointNormals)
    {
        std::cout << "Point normals count: " << pointNormals->GetNumberOfTuples() << std::endl;
        
        // 显示前几个点的法向量
        for (int i = 0; i < std::min(5, (int)pointNormals->GetNumberOfTuples()); i++)
        {
            double normal[3];
            pointNormals->GetTuple(i, normal);
            std::cout << "Point " << i << " normal: (" 
                      << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        }
    }
    else
    {
        std::cout << "ERROR: Point normals not computed!" << std::endl;
    }
    
    if (cellNormals)
    {
        std::cout << "Cell normals count: " << cellNormals->GetNumberOfTuples() << std::endl;
        
        // 显示前几个面的法向量
        for (int i = 0; i < std::min(5, (int)cellNormals->GetNumberOfTuples()); i++)
        {
            double normal[3];
            cellNormals->GetTuple(i, normal);
            std::cout << "Cell " << i << " normal: (" 
                      << normal[0] << ", " << normal[1] << ", " << normal[2] << ")" << std::endl;
        }
    }
    else
    {
        std::cout << "ERROR: Cell normals not computed!" << std::endl;
    }

    // 创建简单的可视化
    vtkNew<vtkPolyDataMapper> mapper;
    mapper->SetInputData(polyDataWithNormals);

    vtkNew<vtkActor> actor;
    actor->SetMapper(mapper);
    actor->GetProperty()->SetColor(0.8, 0.8, 0.8);

    vtkNew<vtkRenderer> renderer;
    renderer->AddActor(actor);
    renderer->SetBackground(0.1, 0.2, 0.4);

    vtkNew<vtkRenderWindow> renderWindow;
    renderWindow->AddRenderer(renderer);
    renderWindow->SetWindowName("VTK Normal Test");
    renderWindow->SetSize(800, 600);

    vtkNew<vtkRenderWindowInteractor> renderWindowInteractor;
    renderWindowInteractor->SetRenderWindow(renderWindow);

    // 设置相机
    vtkCamera* camera = renderer->GetActiveCamera();
    double bounds[6];
    polyDataWithNormals->GetBounds(bounds);
    
    double center[3] = {
        (bounds[0] + bounds[1]) / 2.0,
        (bounds[2] + bounds[3]) / 2.0,
        (bounds[4] + bounds[5]) / 2.0
    };
    
    double size = sqrt((bounds[1] - bounds[0]) * (bounds[1] - bounds[0]) +
                       (bounds[3] - bounds[2]) * (bounds[3] - bounds[2]) +
                       (bounds[5] - bounds[4]) * (bounds[5] - bounds[4]));
    
    camera->SetPosition(center[0], center[1], center[2] + size * 2.0);
    camera->SetFocalPoint(center);
    camera->SetViewUp(0, 1, 0);
    
    renderer->ResetCamera();
    camera->Zoom(0.9);

    std::cout << "Starting render..." << std::endl;
    renderWindow->Render();
    
    std::cout << "Normal calculation test completed successfully!" << std::endl;
    std::cout << "Press 'q' to quit or interact with the model" << std::endl;
    
    renderWindowInteractor->Start();

    return 0;
}
